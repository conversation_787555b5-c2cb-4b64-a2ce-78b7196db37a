from flask import Flask, render_template, session, request, redirect, url_for, jsonify, send_file, Response
from flask_socketio import So<PERSON><PERSON>, emit, join_room, leave_room
import mysql.connector
from datetime import datetime, timedelta
from flask_cors import CORS
from functools import wraps
import threading
import time
import atexit
import io
import json
import base64
import mimetypes
from werkzeug.utils import secure_filename
from file_upload_handler import handle_file_upload

app = Flask(__name__)
app.config['SECRET_KEY'] = 'your-secret-key'
# Session configuration
app.config.update(
    SESSION_COOKIE_SECURE=True,  # Only send cookies over HTTPS
    SESSION_COOKIE_HTTPONLY=True,  # Prevent JavaScript from reading cookies
    SESSION_COOKIE_SAMESITE='Lax',  # Prevent CSRF attacks
    PERMANENT_SESSION_LIFETIME=timedelta(days=7)  # Session lasts for 7 days
)
# Enable CORS
CORS(app)
# Configure socketio for production with enhanced robust settings
socketio = SocketIO(
    app,
    cors_allowed_origins="*",
    async_mode='threading',
    ping_timeout=120,           # Increased from 60 to 120 seconds
    ping_interval=25,
    engineio_logger=True,
    logger=True,
    path='/socket.io',
    manage_session=False,       # Let Flask manage the session
    always_connect=True,        # Always attempt to connect
    max_http_buffer_size=10e6,  # Increased buffer size for larger payloads
    http_compression=True,      # Enable compression
    reconnection=True,          # Enable reconnection on server side
    reconnection_attempts=10,   # Match client-side setting
    reconnection_delay=1000,    # Initial delay in ms
    reconnection_delay_max=10000 # Maximum delay between reconnection attempts
)

# Login required decorator
def login_required(f):
    @wraps(f)
    def decorated_function(*args, **kwargs):
        if 'user_id' not in session:
            return redirect(url_for('landing_page'))
        return f(*args, **kwargs)
    return decorated_function

# Database configuration
db_config = {
    'host': '**************',
    'user': 'giggenius_user',
    'password': 'Happiness1524!',
    'database': 'giggenius',
    'connect_timeout': 10,
    'use_pure': True,
    'autocommit': True,
    'buffered': True,  # Enable buffered cursors by default
    'consume_results': True  # Automatically consume results
}

# Connection management
class ConnectionManager:
    def __init__(self):
        self.connections = {}  # Dictionary to store multiple connections
        self.lock = threading.Lock()
        self.connection_timeout = 300  # 5 minutes timeout
        self.max_connections = 20  # Increased connection limit
        self.active_connections = 0
        self.connection_attempts = 0
        self.last_cleanup = time.time()
        self.cleanup_interval = 60  # Cleanup every minute

    def get_connection(self):
        """Get a database connection with proper tracking and improved pooling"""
        with self.lock:
            current_time = time.time()
            thread_id = threading.get_ident()

            # Periodically clean up stale connections
            if current_time - self.last_cleanup > self.cleanup_interval:
                self._cleanup_stale_connections()
                self.last_cleanup = current_time

            # Check if this thread already has a connection
            if thread_id in self.connections:
                conn_data = self.connections[thread_id]
                # Check if the connection is still alive and not too old
                if (self._is_connection_alive(conn_data['connection']) and
                    current_time - conn_data['last_used'] < self.connection_timeout):
                    # Update last used time
                    self.connections[thread_id]['last_used'] = current_time
                    return conn_data['connection']
                else:
                    # Close the stale connection
                    try:
                        conn_data['connection'].close()
                        print(f"Closed stale connection for thread {thread_id}")
                    except Exception as e:
                        print(f"Error closing stale connection: {e}")
                    # Remove from the pool
                    del self.connections[thread_id]
                    self.active_connections -= 1

            # Create a new connection if we're under the limit
            if self.active_connections < self.max_connections:
                connection = self._create_connection()
                if connection:
                    self.active_connections += 1
                    self.connections[thread_id] = {
                        'connection': connection,
                        'last_used': current_time
                    }
                    print(f"Created new connection for thread {thread_id}. Active: {self.active_connections}")
                    return connection
                else:
                    print(f"Failed to create connection for thread {thread_id}")
            else:
                print(f"Connection pool exhausted. Active: {self.active_connections}/{self.max_connections}")

                # Try to find the oldest connection and reuse it
                oldest_thread = None
                oldest_time = current_time

                for tid, conn_data in self.connections.items():
                    if conn_data['last_used'] < oldest_time:
                        oldest_time = conn_data['last_used']
                        oldest_thread = tid

                if oldest_thread and oldest_thread != thread_id:
                    print(f"Reusing connection from thread {oldest_thread}")
                    conn_data = self.connections[oldest_thread]
                    # Check if it's still alive
                    if self._is_connection_alive(conn_data['connection']):
                        # Move the connection to this thread
                        self.connections[thread_id] = {
                            'connection': conn_data['connection'],
                            'last_used': current_time
                        }
                        del self.connections[oldest_thread]
                        return conn_data['connection']
                    else:
                        # Close and remove the dead connection
                        try:
                            conn_data['connection'].close()
                        except:
                            pass
                        del self.connections[oldest_thread]
                        self.active_connections -= 1

            # As a last resort, create a direct connection outside the pool
            self.connection_attempts += 1
            try:
                print(f"Creating direct connection as fallback (attempt {self.connection_attempts})")
                direct_conn = mysql.connector.connect(**db_config)
                # Don't track this in the pool
                return direct_conn
            except Exception as e:
                print(f"Error creating direct connection: {e}")
                return None

    def _create_connection(self):
        """Create a new database connection with better error handling"""
        max_retries = 3
        retry_delay = 1

        for attempt in range(max_retries):
            try:
                connection = mysql.connector.connect(**db_config)
                # Test the connection
                cursor = connection.cursor()
                cursor.execute("SELECT 1")
                cursor.fetchone()
                cursor.close()
                return connection
            except Exception as e:
                print(f"Error creating connection (attempt {attempt+1}/{max_retries}): {e}")
                if attempt < max_retries - 1:
                    time.sleep(retry_delay)
                    retry_delay *= 2

        return None

    def _is_connection_alive(self, connection):
        """Check if the connection is still alive"""
        if connection is None:
            return False

        try:
            cursor = connection.cursor()
            cursor.execute("SELECT 1")
            cursor.fetchone()
            cursor.close()
            return True
        except Exception as e:
            print(f"Connection is no longer alive: {e}")
            return False

    def _cleanup_stale_connections(self):
        """Clean up stale connections periodically"""
        current_time = time.time()
        threads_to_remove = []

        for thread_id, conn_data in self.connections.items():
            if current_time - conn_data['last_used'] > self.connection_timeout:
                try:
                    conn_data['connection'].close()
                    print(f"Cleaned up stale connection for thread {thread_id}")
                except:
                    pass
                threads_to_remove.append(thread_id)

        # Remove the stale connections
        for thread_id in threads_to_remove:
            del self.connections[thread_id]
            self.active_connections -= 1

        if threads_to_remove:
            print(f"Cleaned up {len(threads_to_remove)} stale connections. Active: {self.active_connections}")

    def release_connection(self, connection=None):
        """Release a connection back to the pool"""
        with self.lock:
            thread_id = threading.get_ident()

            if thread_id in self.connections:
                conn_data = self.connections[thread_id]
                # Update last used time instead of closing
                conn_data['last_used'] = time.time()
                print(f"Released connection for thread {thread_id} back to pool")
            elif connection:
                # If this is a direct connection not in our pool, close it
                try:
                    connection.close()
                    print("Closed direct connection")
                except:
                    pass

    def close_all(self):
        """Close all connections"""

        with self.lock:
            for thread_id, conn_data in list(self.connections.items()):
                try:
                    conn_data['connection'].close()
                    print(f"Closed connection for thread {thread_id} during shutdown")
                except:
                    pass

            self.connections.clear()
            self.active_connections = 0
            print("All database connections closed")

# Create connection manager
conn_manager = ConnectionManager()

# Register shutdown function to close connections
@atexit.register
def shutdown():
    conn_manager.close_all()

# Function to get a connection with enhanced retry and error handling
def get_db_connection():
    """Get a database connection with enhanced retry logic and better error handling"""
    max_retries = 5  # Increased from 3 to 5
    retry_delay = 1
    last_error = None

    for attempt in range(max_retries):
        try:
            connection = conn_manager.get_connection()
            if connection is not None:
                # Test the connection to make sure it's really working
                cursor = connection.cursor()
                cursor.execute("SELECT 1")
                cursor.fetchone()
                cursor.close()
                return connection
        except Exception as e:
            last_error = e
            print(f"Connection test failed on attempt {attempt+1}/{max_retries}: {e}")

            # Try to close the connection if it exists but is faulty
            if 'connection' in locals() and connection is not None:
                try:
                    connection.close()
                except:
                    pass

        # Wait before retrying
        if attempt < max_retries - 1:
            sleep_time = retry_delay * (2 ** attempt)  # Exponential backoff
            print(f"Retrying connection in {sleep_time} seconds...")
            time.sleep(sleep_time)

    # Last resort - create a direct connection with better error handling
    print("All connection attempts failed. Creating direct connection as last resort")
    try:
        direct_conn = mysql.connector.connect(**db_config)
        # Test this connection too
        cursor = direct_conn.cursor()
        cursor.execute("SELECT 1")
        cursor.fetchone()
        cursor.close()
        return direct_conn
    except Exception as e:
        print(f"Failed to create direct connection: {e}")
        if last_error:
            print(f"Original error was: {last_error}")
        raise RuntimeError(f"Could not establish database connection after {max_retries} attempts: {e}")

# Cache decorator for expensive database operations
def cache_result(timeout=300):  # 5 minutes default cache
    def decorator(f):
        cache = {}
        @wraps(f)
        def decorated_function(*args, **kwargs):
            key = str(args) + str(kwargs)
            if key in cache:
                result, timestamp = cache[key]
                if time.time() - timestamp < timeout:
                    return result
            result = f(*args, **kwargs)
            cache[key] = (result, time.time())
            return result
        return decorated_function
    return decorator

@app.route('/')
def landing_page():
    # If user is already logged in, redirect to appropriate page
    if 'user_id' in session:
        if session.get('user_type') == 'admin':
            return redirect(url_for('admin_page'))
        elif session.get('user_type') == 'genius':
            return redirect(url_for('genius_page'))
        elif session.get('user_type') == 'client':
            return redirect(url_for('client_page'))
    return render_template('landing_page.html')


@app.route('/admin_page')
def admin_page():
    """Admin dashboard page - OPTIMIZED FOR PERFORMANCE"""
    # Check if user is authenticated as admin
    if 'user_id' not in session or session.get('user_type') != 'admin':
        return redirect(url_for('landing_page'))

    try:
        conn = get_db_connection()
        cursor = conn.cursor(dictionary=True)

        # Get admin data for the current logged-in admin
        admin_id = session.get('user_id')
        print(f"Admin ID from session: {admin_id}")
        cursor.execute("SELECT * FROM admin WHERE id = %s", (admin_id,))
        admin = cursor.fetchone()
        print(f"Admin data from database: {admin}")

        # PERFORMANCE OPTIMIZATION: Only select essential fields, exclude large BLOB data
        # Get pending genius registrations (without large binary data)
        cursor.execute("""
            SELECT id, first_name, last_name, email, country, position, expertise,
                   hourly_rate, status, created_at, is_pwd, commission_rate, pwd_approval_status
            FROM register_genius
            WHERE status = 'pending'
            ORDER BY created_at DESC
            LIMIT 100
        """)
        register_geniuses = cursor.fetchall()

        # Get approved geniuses (limited fields)
        cursor.execute("""
            SELECT id, first_name, last_name, email, country, position, expertise,
                   hourly_rate, created_at, is_pwd, commission_rate, pwd_approval_status
            FROM approve_genius
            ORDER BY created_at DESC
            LIMIT 100
        """)
        approve_geniuses = cursor.fetchall()

        # Get pending client registrations (without large binary data)
        cursor.execute("""
            SELECT id, first_name, last_name, work_email, position, business_name,
                   country, status, created_at, is_pwd, commission_rate, pwd_approval_status
            FROM register_client
            WHERE status = 'pending'
            ORDER BY created_at DESC
            LIMIT 100
        """)
        register_clients = cursor.fetchall()

        # Get approved clients (limited fields)
        cursor.execute("""
            SELECT id, first_name, last_name, work_email, position, business_name,
                   country, created_at, is_pwd, commission_rate, pwd_approval_status
            FROM approve_client
            ORDER BY created_at DESC
            LIMIT 100
        """)
        approve_clients = cursor.fetchall()

        # Get PWD pending approvals for both geniuses and clients
        cursor.execute("""
            SELECT id, first_name, last_name, email, country, position,
                   created_at, is_pwd, commission_rate, pwd_approval_status, 'genius' as user_type
            FROM register_genius
            WHERE is_pwd = 1 AND pwd_approval_status = 'pending'
            UNION ALL
            SELECT id, first_name, last_name, work_email as email, country, position,
                   created_at, is_pwd, commission_rate, pwd_approval_status, 'client' as user_type
            FROM register_client
            WHERE is_pwd = 1 AND pwd_approval_status = 'pending'
            ORDER BY created_at DESC
            LIMIT 50
        """)
        pwd_pending_users = cursor.fetchall()

        cursor.close()
        conn.close()

        # Create a default admin object if not found in database or add missing fields
        if not admin:
            admin = {
                'id': admin_id,
                'username': session.get('username', 'Admin'),
                'profile_picture_url': '/static/img/logo.png'
            }
        else:
            # Ensure admin object has profile_picture_url field
            if 'profile_picture_url' not in admin or not admin['profile_picture_url']:
                admin['profile_picture_url'] = '/static/img/logo.png'

        return render_template('admin_page.html',
                             admin=admin,
                             register_geniuses=register_geniuses,
                             approve_geniuses=approve_geniuses,
                             register_clients=register_clients,
                             approve_clients=approve_clients,
                             pwd_pending_users=pwd_pending_users)

    except Exception as e:
        print(f"Error loading admin page: {e}")
        # Create a default admin object for error case
        admin = {
            'id': session.get('user_id', 1),
            'username': session.get('username', 'Admin'),
            'profile_picture_url': '/static/img/logo.png'
        }
        return render_template('admin_page.html',
                             admin=admin,
                             register_geniuses=[],
                             approve_geniuses=[],
                             register_clients=[],
                             approve_clients=[],
                             pwd_pending_users=[])

@app.route('/test_admin_db')
def test_admin_db():
    """Test route to check admin table and data"""
    try:
        conn = get_db_connection()
        cursor = conn.cursor(dictionary=True, buffered=True)

        # Check if admin table exists
        check_table_query = """
            SELECT COUNT(*)
            FROM information_schema.tables
            WHERE table_schema = 'giggenius'
            AND table_name = 'admin'
        """
        cursor.execute(check_table_query)
        table_exists = cursor.fetchone()['COUNT(*)'] > 0

        if not table_exists:
            return jsonify({
                'success': False,
                'error': 'Admin table does not exist',
                'table_exists': False
            })

        # Check admin data
        admin_query = """
            SELECT id, username, created_at
            FROM admin
            WHERE username = %s
        """
        cursor.execute(admin_query, ('<EMAIL>',))
        admin_user = cursor.fetchone()

        return jsonify({
            'success': True,
            'table_exists': True,
            'admin_user_exists': bool(admin_user),
            'admin_data': admin_user if admin_user else None
        })

    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        })
    finally:
        if 'cursor' in locals() and cursor:
            try:
                cursor.close()
            except:
                pass
        if 'conn' in locals() and conn:
            try:
                conn.close()
            except:
                pass

@app.route('/login', methods=['GET', 'POST'])
def login():
    # If user is already logged in, redirect to appropriate page
    if 'user_id' in session:
        if session.get('user_type') == 'admin':
            return jsonify(success=True, redirect=url_for('admin_page'), replace_history=True)
        elif session.get('user_type') == 'genius':
            return jsonify(success=True, redirect=url_for('genius_page'), replace_history=True)
        else:
            return jsonify(success=True, redirect=url_for('client_page'), replace_history=True)

    # Handle GET request - render login page
    if request.method == 'GET':
        return render_template('landing_page.html')

    # Handle POST request for login
    # Get form data
    email = request.form.get('email')
    password = request.form.get('password')
    user_type = request.form.get('user_type', 'auto')  # Get user_type from form, default to auto

    # Validate user input
    if not email or not password:
        return jsonify(success=False, error="Email and password are required")

    try:
        # Get a connection
        conn = get_db_connection()
        cursor = conn.cursor(dictionary=True)

        # Auto-detect user type if set to 'auto'
        if user_type == 'auto':
            # First check in admin table
            query = """
                SELECT id, username, password, profile
                FROM admin
                WHERE username = %s
            """
            cursor.execute(query, (email,))
            user = cursor.fetchone()

            if user and user['password'] == password:
                user_type = 'admin'
            else:
                # If not found in admin table, check genius table
                query = """
                    SELECT id, email, first_name, last_name, password,
                           IF(profile_photo IS NULL, 0, 1) as has_photo
                    FROM approve_genius
                    WHERE email = %s
                """
                cursor.execute(query, (email,))
                user = cursor.fetchone()

                if user and user['password'] == password:
                    user_type = 'genius'
                else:
                    # If not found in genius table, check client table
                    query = """
                        SELECT id, work_email, first_name, last_name, password,
                               IF(profile_photo IS NULL, 0, 1) as has_photo,
                               business_name, position
                        FROM approve_client
                        WHERE work_email = %s
                    """
                    cursor.execute(query, (email,))
                    user = cursor.fetchone()

                    if user and user['password'] == password:
                        user_type = 'client'
                    else:
                        user = None
        else:
            # Check in the specified table
            if user_type == 'admin':
                query = """
                    SELECT id, username, password, profile
                    FROM admin
                    WHERE username = %s
                """
                cursor.execute(query, (email,))
                user = cursor.fetchone()
                if not (user and user['password'] == password):
                    user = None
            elif user_type == 'genius':
                query = """
                    SELECT id, email, first_name, last_name, password,
                           IF(profile_photo IS NULL, 0, 1) as has_photo
                    FROM approve_genius
                    WHERE email = %s
                """
                cursor.execute(query, (email,))
                user = cursor.fetchone()
                if not (user and user['password'] == password):
                    user = None
            else:  # client
                query = """
                    SELECT id, work_email, first_name, last_name, password,
                           IF(profile_photo IS NULL, 0, 1) as has_photo,
                           business_name, position
                    FROM approve_client
                    WHERE work_email = %s
                """
                cursor.execute(query, (email,))
                user = cursor.fetchone()
                if not (user and user['password'] == password):
                    user = None

        if not user:
            cursor.close()
            conn.close()
            return jsonify(success=False, error="Invalid email or password")

        # User found, set up session
        session.clear()
        session['user_id'] = user['id']
        session['user_type'] = user_type

        if user_type == 'admin':
            session['email'] = user['username']  # Use username as email for admin
            session['name'] = 'Administrator'
            session['first_name'] = 'Admin'
            session['last_name'] = 'User'
            session['has_profile_photo'] = bool(user.get('profile'))
        elif user_type == 'genius':
            session['email'] = user['email']
            session['name'] = f"{user['first_name']} {user['last_name']}"
            session['first_name'] = user['first_name']
            session['last_name'] = user['last_name']
            session['has_profile_photo'] = bool(user['has_photo'])
        else:  # client
            session['email'] = user['work_email']
            session['business_name'] = user['business_name']
            session['position'] = user['position']
            session['name'] = f"{user['first_name']} {user['last_name']}"
            session['first_name'] = user['first_name']
            session['last_name'] = user['last_name']
            session['has_profile_photo'] = bool(user['has_photo'])

        session.permanent = True

        cursor.close()
        conn.close()

        # Determine redirect URL based on user type
        if user_type == 'admin':
            redirect_url = url_for('admin_page')
        elif user_type == 'genius':
            redirect_url = url_for('genius_page')
        else:  # client
            redirect_url = url_for('client_page')

        return jsonify(success=True, redirect=redirect_url, replace_history=True)

    except mysql.connector.Error as e:
        print(f"Database error: {e}")
        # Make sure to consume any results before closing
        if 'cursor' in locals() and cursor:
            try:
                # Consume any unread results
                if cursor.with_rows:
                    cursor.fetchall()
            except:
                pass
            cursor.close()
        if 'conn' in locals() and conn:
            conn.close()
        return jsonify(success=False, error="Database error. Please try again.")
    except Exception as e:
        print(f"Login error: {e}")
        # Make sure to consume any results before closing
        if 'cursor' in locals() and cursor:
            try:
                # Consume any unread results
                if cursor.with_rows:
                    cursor.fetchall()
            except:
                pass
            cursor.close()
        if 'conn' in locals() and conn:
            conn.close()
        return jsonify(success=False, error="An error occurred. Please try again.")
    finally:
        if 'cursor' in locals():
            cursor.close()
        if 'conn' in locals():
            conn.close()

@socketio.on('send_message')
def handle_send_message(data):
    sender_id = session.get('user_id')
    receiver_id = data.get('receiver_id')
    message = data.get('message')
    related_to_job_id = data.get('related_to_job_id')
    related_to_application_id = data.get('related_to_application_id')

    # Debug logging
    print(f"Received message from user {sender_id} to user {receiver_id}")
    print(f"Socket SID: {request.sid}")
    print(f"Message data: {data}")

    if not sender_id or not receiver_id or not message:
        emit('error', {'msg': 'Invalid data'})
        return

    try:
        conn = get_db_connection()
        cursor = conn.cursor(dictionary=True)

        # Check if messages table exists and has the correct structure
        try:
            check_table_query = """
                SELECT COUNT(*)
                FROM information_schema.tables
                WHERE table_schema = 'giggenius'
                AND table_name = 'messages'
            """
            cursor.execute(check_table_query)
            table_exists = cursor.fetchone()['COUNT(*)'] > 0

            if not table_exists:
                # Create messages table with the correct structure
                create_table_query = """
                    CREATE TABLE messages (
                        id INT AUTO_INCREMENT PRIMARY KEY,
                        sender_id INT NOT NULL,
                        sender_type VARCHAR(50) DEFAULT 'user',
                        receiver_id INT NOT NULL,
                        receiver_type VARCHAR(50) DEFAULT 'user',
                        message_text TEXT NOT NULL,
                        is_read BOOLEAN DEFAULT FALSE,
                        is_auto BOOLEAN DEFAULT FALSE,
                        related_to_job_id INT NULL,
                        related_to_application_id INT NULL,
                        message_type VARCHAR(50) DEFAULT 'text',
                        timestamp DATETIME NOT NULL,
                        status VARCHAR(50) DEFAULT 'sent',
                        is_deleted BOOLEAN DEFAULT FALSE,
                        deleted_by_sender BOOLEAN DEFAULT FALSE,
                        deleted_by_receiver BOOLEAN DEFAULT FALSE,
                        reply_to_id INT NULL,
                        replied_message_text TEXT NULL,
                        file_data LONGBLOB NULL,
                        file_name VARCHAR(255) NULL,
                        file_mime_type VARCHAR(100) NULL,
                        file_url VARCHAR(255) NULL,
                        INDEX (sender_id),
                        INDEX (receiver_id),
                        INDEX (related_to_job_id),
                        INDEX (related_to_application_id),
                        INDEX (reply_to_id)
                    )
                """
                cursor.execute(create_table_query)
                conn.commit()
                print("Successfully created messages table")
            else:
                # Check if the table has the correct structure
                check_columns_query = """
                    SELECT COLUMN_NAME
                    FROM INFORMATION_SCHEMA.COLUMNS
                    WHERE TABLE_SCHEMA = 'giggenius'
                    AND TABLE_NAME = 'messages'
                """
                cursor.execute(check_columns_query)
                columns = [row['COLUMN_NAME'] for row in cursor.fetchall()]

                # Check for required columns and add them if missing
                required_columns = {
                    'sender_type': "VARCHAR(50) DEFAULT 'user' AFTER sender_id",
                    'receiver_type': "VARCHAR(50) DEFAULT 'user' AFTER receiver_id",
                    'message_text': "TEXT NOT NULL AFTER receiver_type",
                    'is_read': "BOOLEAN DEFAULT FALSE AFTER message_text",
                    'is_auto': "BOOLEAN DEFAULT FALSE AFTER is_read",
                    'related_to_job_id': "INT NULL AFTER is_auto",
                    'related_to_application_id': "INT NULL AFTER related_to_job_id",
                    'message_type': "VARCHAR(50) DEFAULT 'text' AFTER related_to_application_id",
                    'timestamp': "DATETIME NOT NULL AFTER message_type",
                    'status': "VARCHAR(50) DEFAULT 'sent' AFTER timestamp",
                    'is_deleted': "BOOLEAN DEFAULT FALSE AFTER status",
                    'deleted_by_sender': "BOOLEAN DEFAULT FALSE AFTER is_deleted",
                    'deleted_by_receiver': "BOOLEAN DEFAULT FALSE AFTER deleted_by_sender",
                    'reply_to_id': "INT NULL AFTER deleted_by_receiver",
                    'replied_message_text': "TEXT NULL AFTER reply_to_id",
                    'file_data': "LONGBLOB NULL AFTER replied_message_text",
                    'file_name': "VARCHAR(255) NULL AFTER file_data",
                    'file_mime_type': "VARCHAR(100) NULL AFTER file_name",
                    'file_url': "VARCHAR(255) NULL AFTER file_mime_type"
                }

                # Special case for message vs message_text
                if 'message_text' not in columns and 'message' in columns:
                    # Rename message column to message_text
                    alter_query = """
                        ALTER TABLE messages
                        CHANGE COLUMN message message_text TEXT NOT NULL
                    """
                    cursor.execute(alter_query)
                    conn.commit()
                    print("Renamed 'message' column to 'message_text'")
                    columns.append('message_text')

                # Add all missing columns
                for column, definition in required_columns.items():
                    if column not in columns:
                        alter_query = f"""
                            ALTER TABLE messages
                            ADD COLUMN {column} {definition}
                        """
                        cursor.execute(alter_query)
                        conn.commit()
                        print(f"Added '{column}' column to messages table")
        except Exception as e:
            print(f"Error checking/updating messages table structure: {e}")
            # Continue with the process, we'll handle errors later

        # Insert message into database
        insert_query = """
            INSERT INTO messages (
                sender_id, sender_type, receiver_id, receiver_type, message_text,
                is_read, is_auto, related_to_job_id, related_to_application_id,
                message_type, timestamp, status, is_deleted, deleted_by_sender,
                deleted_by_receiver
            ) VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, NOW(), %s, %s, %s, %s)
        """

        # Determine user types
        sender_type = 'client' if session.get('user_type') == 'client' else 'genius'
        receiver_type = 'client' if sender_type == 'genius' else 'genius'

        # Use 'sending' status initially to match the client-side temporary message
        cursor.execute(
            insert_query,
            (
                sender_id,
                sender_type,
                receiver_id,
                receiver_type,
                message,
                False,  # is_read
                False,  # is_auto
                related_to_job_id,
                related_to_application_id,
                'text',  # message_type
                'sending',  # status - use 'sending' to match client-side
                False,   # is_deleted
                False,   # deleted_by_sender
                False    # deleted_by_receiver
            )
        )
        conn.commit()
        msg_id = cursor.lastrowid

        # Get the message with formatted timestamp
        get_message_query = """
            SELECT
                id, sender_id, sender_type, receiver_id, receiver_type,
                message_text as message, is_read, is_auto,
                related_to_job_id, related_to_application_id,
                message_type, timestamp, status, is_deleted,
                deleted_by_sender, deleted_by_receiver,
                reply_to_id, replied_message_text,
                file_data, file_name, file_mime_type, file_url
            FROM messages
            WHERE id = %s
        """
        cursor.execute(get_message_query, (msg_id,))
        msg = cursor.fetchone()

        cursor.close()
        conn.close()

        # Format dates for JSON serialization
        if msg:
            # Convert msg to a regular dict to make it mutable
            msg = dict(msg)

            # First collect all datetime fields that need formatting
            datetime_fields = {}
            formatted_fields = {}

            for key, value in msg.items():
                if isinstance(value, datetime):
                    datetime_fields[key] = value

            # Now update the dictionary with formatted values
            for key, value in datetime_fields.items():
                if key == 'created_at':
                    formatted_fields['created_at_formatted'] = value.strftime('%Y-%m-%d %H:%M:%S')
                msg[key] = value.strftime('%Y-%m-%dT%H:%M:%S.000Z')

            # Add any additional formatted fields
            for key, value in formatted_fields.items():
                msg[key] = value

        # Emit message to both sender and receiver
        # Always convert IDs to strings to ensure proper room targeting
        sender_room = str(sender_id)
        receiver_room = str(receiver_id)

        # Send to receiver's room
        emit('receive_message', msg, room=receiver_room)

        # Send to sender's room (in case they have multiple tabs/devices)
        emit('receive_message', msg, room=sender_room)

        # Also send to sender's session for immediate feedback
        emit('receive_message', msg, room=request.sid)

        # Schedule a task to update the message status to 'sent' after a short delay
        # This allows the clock icon to be displayed for a moment before changing to checkmark
        def update_message_status():
            try:
                time.sleep(2)  # Wait 2 seconds
                conn = get_db_connection()
                cursor = conn.cursor(dictionary=True)

                # Update the message status to 'sent'
                update_query = """
                    UPDATE messages
                    SET status = 'sent'
                    WHERE id = %s
                """
                cursor.execute(update_query, (msg_id,))
                conn.commit()

                # Get the updated message
                select_query = """
                    SELECT
                        id, sender_id, sender_type, receiver_id, receiver_type,
                        message_text as message, is_read, is_auto,
                        related_to_job_id, related_to_application_id,
                        message_type, timestamp, status, is_deleted,
                        deleted_by_sender, deleted_by_receiver,
                        reply_to_id, replied_message_text,
                        file_name, file_mime_type, file_url
                    FROM messages
                    WHERE id = %s
                """
                cursor.execute(select_query, (msg_id,))
                updated_msg = cursor.fetchone()

                if updated_msg:
                    # Format the message for JSON serialization
                    formatted_msg = dict(updated_msg)
                    for key, value in formatted_msg.items():
                        if isinstance(value, datetime):
                            formatted_msg[key] = value.strftime('%Y-%m-%dT%H:%M:%S.000Z')

                    # Emit a status update event instead of a full message
                    # This prevents duplicate messages in the UI
                    socketio.emit('message_status_update', {
                        'message_id': msg_id,
                        'status': 'sent'
                    }, room=sender_room)
                    socketio.emit('message_status_update', {
                        'message_id': msg_id,
                        'status': 'sent'
                    }, room=request.sid)

                cursor.close()
                conn.close()
            except Exception as e:
                print(f"Error updating message status: {e}")

        # Start the status update in a separate thread
        status_thread = threading.Thread(target=update_message_status)
        status_thread.daemon = True
        status_thread.start()

        # Return the message
        return msg

    except Exception as e:
        print(f"Error sending message: {e}")
        emit('error', {'msg': f'Error sending message: {str(e)}'})
        return None

@socketio.on('join')
def on_join(data=None):
    """Socket.IO event handler for joining user-specific rooms"""
    user_id = session.get('user_id')
    # Extract additional data if provided
    user_type = session.get('user_type')

    # If data was provided, log it for debugging
    if data:
        print(f"Join event with data: {data}")
        # Extract user_id from data if provided and not in session
        if not user_id and data.get('user_id'):
            user_id = data.get('user_id')
            print(f"Using user_id from data: {user_id}")

    if user_id:
        # Always convert user_id to string to ensure consistent room naming
        user_id_str = str(user_id)

        # Join a room with the user's ID
        join_room(user_id_str)
        print(f"User {user_id} ({user_type}) joined room {user_id_str}")

        # Also join a room with the session ID for messages sent by this user
        join_room(request.sid)
        print(f"User {user_id} joined room {request.sid}")

        # Emit a confirmation back to the client
        emit('join_confirmation', {
            'user_id': user_id,
            'user_type': user_type,
            'room': user_id_str,
            'sid': request.sid
        })

@socketio.on('leave')
def on_leave():
    user_id = session.get('user_id')
    if user_id:
        # Leave the room with the user's ID
        leave_room(user_id)
        print(f"User {user_id} left room {user_id}")

@socketio.on('add_reaction')
def handle_add_reaction(data):
    """Socket.IO event handler to add a reaction to a message"""
    user_id = session.get('user_id')
    user_type = session.get('user_type')
    message_id = data.get('message_id')
    emoji = data.get('emoji')

    if not user_id or not message_id or not emoji:
        emit('error', {'msg': 'Invalid data'})
        return

    try:
        conn = get_db_connection()
        cursor = conn.cursor(dictionary=True)

        # Check if the message exists and user has access to it
        check_message_query = """
            SELECT id, sender_id, receiver_id
            FROM messages
            WHERE id = %s
        """
        cursor.execute(check_message_query, (message_id,))
        message = cursor.fetchone()

        if not message:
            cursor.close()
            conn.close()
            emit('error', {'msg': 'Message not found'})
            return

        # Check if user is either sender or receiver
        if message['sender_id'] != user_id and message['receiver_id'] != user_id:
            cursor.close()
            conn.close()
            emit('error', {'msg': "You don't have permission to react to this message"})
            return

        # Check if reactions table exists
        check_table_query = """
            SELECT COUNT(*)
            FROM information_schema.tables
            WHERE table_schema = 'giggenius'
            AND table_name = 'reactions'
        """
        cursor.execute(check_table_query)
        table_exists = cursor.fetchone()['COUNT(*)'] > 0

        if not table_exists:
            # Create reactions table
            create_table_query = """
                CREATE TABLE reactions (
                    id INT AUTO_INCREMENT PRIMARY KEY,
                    message_id INT NOT NULL,
                    user_id INT NOT NULL,
                    user_type VARCHAR(50) NOT NULL,
                    emoji VARCHAR(10) NOT NULL,
                    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                    INDEX (message_id),
                    INDEX (user_id),
                    UNIQUE KEY unique_reaction (message_id, user_id, emoji)
                )
            """
            cursor.execute(create_table_query)
            conn.commit()
            print("Created reactions table")

        # Add or update reaction
        insert_query = """
            INSERT INTO reactions (message_id, user_id, user_type, emoji)
            VALUES (%s, %s, %s, %s)
            ON DUPLICATE KEY UPDATE updated_at = CURRENT_TIMESTAMP
        """
        cursor.execute(insert_query, (message_id, user_id, user_type, emoji))
        conn.commit()

        # Get the reaction ID
        reaction_id = cursor.lastrowid

        # Get the updated reaction
        get_reaction_query = """
            SELECT * FROM reactions WHERE id = %s
        """
        cursor.execute(get_reaction_query, (reaction_id,))
        reaction = cursor.fetchone()

        cursor.close()
        conn.close()

        # Format the reaction
        formatted_reaction = dict(reaction)
        # Convert datetime objects to strings
        for key, value in formatted_reaction.items():
            if isinstance(value, datetime):
                formatted_reaction[key] = value.strftime('%Y-%m-%dT%H:%M:%S.000Z')

        # Emit event to both sender and receiver
        emit('reaction_added', {
            'message_id': message_id,
            'reaction': formatted_reaction
        }, room=str(message["sender_id"]))

        emit('reaction_added', {
            'message_id': message_id,
            'reaction': formatted_reaction
        }, room=str(message["receiver_id"]))

        # Also broadcast to all connected clients to ensure everyone gets the update
        emit('reaction_added', {
            'message_id': message_id,
            'reaction': formatted_reaction
        }, broadcast=True)

    except Exception as e:
        print(f"Error adding reaction via Socket.IO: {e}")
        emit('error', {'msg': f'Error adding reaction: {str(e)}'})

@socketio.on('remove_reaction')
def handle_remove_reaction(data):
    """Socket.IO event handler to remove a reaction from a message"""
    user_id = session.get('user_id')
    message_id = data.get('message_id')
    emoji = data.get('emoji')

    if not user_id or not message_id or not emoji:
        emit('error', {'msg': 'Invalid data'})
        return

    try:
        conn = get_db_connection()
        cursor = conn.cursor(dictionary=True)

        # Check if the reaction exists
        check_query = """
            SELECT r.*, m.sender_id, m.receiver_id
            FROM reactions r
            JOIN messages m ON r.message_id = m.id
            WHERE r.message_id = %s AND r.user_id = %s AND r.emoji = %s
        """
        cursor.execute(check_query, (message_id, user_id, emoji))
        reaction = cursor.fetchone()

        if not reaction:
            cursor.close()
            conn.close()
            emit('error', {'msg': 'Reaction not found'})
            return

        # Delete the reaction
        delete_query = """
            DELETE FROM reactions
            WHERE message_id = %s AND user_id = %s AND emoji = %s
        """
        cursor.execute(delete_query, (message_id, user_id, emoji))
        conn.commit()

        cursor.close()
        conn.close()

        # Emit event to both sender and receiver
        emit('reaction_removed', {
            'message_id': message_id,
            'user_id': user_id,
            'emoji': emoji
        }, room=str(reaction["sender_id"]))

        emit('reaction_removed', {
            'message_id': message_id,
            'user_id': user_id,
            'emoji': emoji
        }, room=str(reaction["receiver_id"]))

        # Also broadcast to all connected clients to ensure everyone gets the update
        emit('reaction_removed', {
            'message_id': message_id,
            'user_id': user_id,
            'emoji': emoji
        }, broadcast=True)

    except Exception as e:
        print(f"Error removing reaction via Socket.IO: {e}")
        emit('error', {'msg': f'Error removing reaction: {str(e)}'})

@socketio.on('mark_messages_read')
def handle_mark_messages_read(data):
    """Socket.IO event handler to mark messages as read in real-time with improved error handling"""
    user_id = session.get('user_id')
    contact_id = data.get('contact_id')
    conn = None
    cursor = None

    if not user_id or not contact_id:
        emit('error', {'msg': 'Invalid data'})
        return

    try:
        # Get a database connection with our improved connection manager
        print(f"Marking messages as read from {contact_id} to {user_id}")
        conn = get_db_connection()

        if not conn:
            raise RuntimeError("Could not establish database connection")

        cursor = conn.cursor(dictionary=True)

        # Update messages where current user is the receiver and the sender is the contact
        update_query = """
            UPDATE messages
            SET is_read = TRUE
            WHERE receiver_id = %s AND sender_id = %s AND is_read = FALSE
        """
        cursor.execute(update_query, (user_id, contact_id))
        conn.commit()

        rows_affected = cursor.rowcount
        print(f"Marked {rows_affected} messages as read")

        # Get updated unread count for this contact
        unread_query = """
            SELECT COUNT(*) as unread_count
            FROM messages
            WHERE receiver_id = %s AND sender_id = %s AND is_read = FALSE
        """
        cursor.execute(unread_query, (user_id, contact_id))
        unread_result = cursor.fetchone()
        unread_count = unread_result['unread_count'] if unread_result else 0

        # Get the updated messages to reflect read status - limit to fewer messages for performance
        messages_query = """
            SELECT
                id, sender_id, sender_type, receiver_id, receiver_type,
                message_text as message, is_read, is_auto,
                related_to_job_id, related_to_application_id,
                message_type, timestamp, status, is_deleted,
                deleted_by_sender, deleted_by_receiver,
                reply_to_id, replied_message_text,
                file_name, file_mime_type, file_url
            FROM messages
            WHERE (sender_id = %s AND receiver_id = %s)
               OR (sender_id = %s AND receiver_id = %s)
            ORDER BY timestamp DESC
            LIMIT 10
        """
        cursor.execute(messages_query, (user_id, contact_id, contact_id, user_id))
        messages = cursor.fetchall()

        # Format dates for JSON serialization
        formatted_messages = []
        for msg in messages:
            formatted_msg = dict(msg)

            # First identify all datetime fields
            datetime_fields = {}
            for key, value in formatted_msg.items():
                if isinstance(value, datetime):
                    datetime_fields[key] = value

            # Then format them
            for key, value in datetime_fields.items():
                formatted_msg[key] = value.strftime('%Y-%m-%dT%H:%M:%S.000Z')

            formatted_messages.append(formatted_msg)

        # Emit event to update UI for the current user
        emit('messages_marked_read', {
            'contact_id': contact_id,
            'messages_read': rows_affected,
            'unread_count': unread_count,
            'messages': formatted_messages
        }, room=str(user_id))  # Convert user_id to string for consistent room naming

        # Also emit to the current socket
        emit('messages_marked_read', {
            'contact_id': contact_id,
            'messages_read': rows_affected,
            'unread_count': unread_count,
            'messages': formatted_messages
        })

        print(f"Successfully emitted messages_marked_read event for user {user_id}")

    except Exception as e:
        print(f"Error marking messages as read: {e}")
        # Send a more detailed error message for debugging
        error_details = str(e)
        if 'MySQL Connection not available' in error_details:
            error_details += " - Connection pool may be exhausted or database server is unreachable"

        emit('error', {'msg': f'Error marking messages as read: {error_details}'})

        # Try to use the REST API fallback
        try:
            emit('fallback_mark_read', {'contact_id': contact_id})
        except:
            pass
    finally:
        # Ensure resources are properly closed
        if cursor:
            try:
                cursor.close()
            except:
                pass
        if conn:
            try:
                # Use our improved release_connection method
                conn_manager.release_connection(conn)
            except Exception as e:
                print(f"Error releasing connection: {e}")
                try:
                    conn.close()
                except:
                    pass

@app.route('/genius_page')
@login_required
def genius_page():
    if session.get('user_type') != 'genius':
        return redirect(url_for('landing_page'))

    # Initialize default values
    jobs = []
    clients = []
    genius = {
        'id': session.get('user_id'),
        'first_name': session.get('first_name', 'User'),
        'last_name': session.get('last_name', ''),
        'email': session.get('email', ''),
        'profile_picture_url': '/static/img/default-avatar.png',
        'position': 'Freelancer',
        'hourly_rate': 0,
        'country': 'Not specified'
    }

    # Fetch available jobs from job_submissions table
    try:
        conn = get_db_connection()
        cursor = conn.cursor(dictionary=True, buffered=True)

        # Get only published jobs with client information
        query = """
            SELECT js.id, js.client_id, js.title, js.description, js.project_size,
                   js.project_description, js.duration, js.experience_level,
                    js.budget_type, js.budget_amount,
                   js.category, js.specialty, js.skills, js.created_at, js.job_type,
                   js.status,
                   ac.first_name, ac.last_name, ac.country, ac.position,
                   ac.business_name, ac.business_logo, ac.industry
            FROM job_submissions js
            JOIN approve_client ac ON js.client_id = ac.id
            WHERE js.status = 'publish'
            ORDER BY js.created_at DESC
            LIMIT 20
        """
        cursor.execute(query)
        jobs = cursor.fetchall()

        # Format dates and process job data
        for job in jobs:
            if 'created_at' in job and job['created_at']:
                # Format the date to a more readable format
                job['created_at'] = job['created_at'].strftime('%B %d, %Y')

            # Ensure all fields have values to avoid template errors
            for key in ['title', 'description', 'project_size', 'budget_amount', 'category']:
                if key not in job or job[key] is None:
                    job[key] = 'Not specified'

            # Parse skills if it's a JSON string
            if 'skills' in job and job['skills'] and isinstance(job['skills'], str):
                try:
                    job['skills_list'] = json.loads(job['skills'])
                except json.JSONDecodeError:
                    job['skills_list'] = []
            else:
                job['skills_list'] = []

        # Get genius information
        genius_query = """
            SELECT id, first_name, last_name, email, profile_photo, position, hourly_rate, country, introduction
            FROM approve_genius
            WHERE id = %s
        """
        cursor.execute(genius_query, (session.get('user_id'),))
        genius_data = cursor.fetchone()

        # Update with database values if available
        if genius_data:
            genius['first_name'] = genius_data['first_name'] or genius['first_name']
            genius['last_name'] = genius_data['last_name'] or genius['last_name']
            genius['email'] = genius_data['email'] or genius['email']

            if genius_data['profile_photo']:
                genius['profile_picture_url'] = genius_data['profile_photo']

            if genius_data['position']:
                genius['position'] = genius_data['position']

            if genius_data['hourly_rate']:
                genius['hourly_rate'] = genius_data['hourly_rate']

            if genius_data['country']:
                genius['country'] = genius_data['country']

            if genius_data.get('introduction'):
                genius['introduction'] = genius_data['introduction']
            else:
                genius['introduction'] = ''

        # Get all client information for the template
        client_ids = list(set([job['client_id'] for job in jobs]))
        clients = []

        if client_ids:
            placeholders = ', '.join(['%s'] * len(client_ids))
            client_query = f"""
                SELECT id, first_name, last_name, country, position,
                       business_name, business_logo, industry
                FROM approve_client
                WHERE id IN ({placeholders})
            """
            cursor.execute(client_query, client_ids)
            clients = cursor.fetchall()

    except Exception as e:
        print(f"Error fetching data: {e}")
        # Keep default values initialized above

    finally:
        # Ensure proper cleanup
        if 'cursor' in locals() and cursor:
            try:
                cursor.close()
            except:
                pass
        if 'conn' in locals() and conn:
            try:
                conn.close()
            except:
                pass

    # Add pagination variables to prevent template errors
    total_pages = 1
    current_page = 1

    return render_template('genius_page.html', jobs=jobs, genius=genius, clients=clients, total_pages=total_pages, current_page=current_page)

@app.route('/client_page')
@login_required
def client_page():
    if session.get('user_type') != 'client':
        return redirect(url_for('landing_page'))

    # Initialize default values
    client = {
        'id': session.get('user_id'),
        'first_name': session.get('first_name', 'User'),
        'last_name': session.get('last_name', ''),
        'email': session.get('email', ''),
        'business_name': session.get('business_name', 'Company'),
        'position': session.get('position', 'Client'),
        'country': 'Not specified',
        'profile_picture_url': '/static/img/default-avatar.png',
        'introduction': ''
    }
    jobs = []
    draft_jobs = []
    stats = {
        'total_hired': 0,
        'total_spent': 0,
        'posted_jobs': 0,
        'total_applications': 0
    }

    # Fetch client information from the database
    try:
        conn = get_db_connection()
        cursor = conn.cursor(dictionary=True, buffered=True)

        # Get client data
        query = """
            SELECT id, first_name, last_name, work_email, business_name,
                   position, country, profile_photo, introduction, created_at
            FROM approve_client
            WHERE id = %s
        """
        cursor.execute(query, (session.get('user_id'),))
        client_data = cursor.fetchone()

        # Update with database values if available
        if client_data:
            client['first_name'] = client_data['first_name'] or client['first_name']
            client['last_name'] = client_data['last_name'] or client['last_name']
            client['email'] = client_data['work_email'] or client['email']
            client['business_name'] = client_data['business_name'] or client['business_name']
            client['position'] = client_data['position'] or client['position']

            if client_data['country']:
                client['country'] = client_data['country']

            if client_data['profile_photo']:
                client['profile_picture_url'] = client_data['profile_photo']

            if client_data['introduction']:
                client['introduction'] = client_data['introduction']

            if client_data['created_at']:
                client['created_at'] = client_data['created_at']

        # Get job submissions for this client with application counts in a single query
        jobs_query = """
            SELECT js.id, js.title, js.description, js.project_size, js.duration,
                   js.experience_level, js.budget_type, js.budget_amount, js.category,
                   js.specialty, js.skills, js.job_type, js.created_at, js.country, js.status,
                   COALESCE(app_counts.application_count, 0) as application_count
            FROM job_submissions js
            LEFT JOIN (
                SELECT job_id, COUNT(*) as application_count
                FROM applications
                GROUP BY job_id
            ) app_counts ON js.id = app_counts.job_id
            WHERE js.client_id = %s
            ORDER BY js.created_at DESC
        """
        cursor.execute(jobs_query, (session.get('user_id'),))
        jobs = cursor.fetchall()

        # Get draft jobs specifically
        draft_jobs_query = """
            SELECT id, title, description, category, specialty, skills, created_at
            FROM job_submissions
            WHERE client_id = %s AND status = 'draft'
            ORDER BY created_at DESC
        """
        cursor.execute(draft_jobs_query, (session.get('user_id'),))
        draft_jobs = cursor.fetchall()

        # Format dates and calculate totals
        total_applications = 0
        total_spent = 0

        for job in jobs:
            if 'created_at' in job and job['created_at']:
                # Format the date to a more readable format
                job['created_at_formatted'] = job['created_at'].strftime('%B %d, %Y')

            # Add to total applications
            total_applications += job.get('application_count', 0)

            # Add to total spent (if budget is available)
            if job.get('budget_amount') and job.get('budget_type') == 'fixed':
                try:
                    total_spent += float(job['budget_amount'])
                except (ValueError, TypeError):
                    pass  # Skip invalid budget amounts

        # Get count of hired geniuses
        hired_query = """
            SELECT COUNT(DISTINCT genius_id) as hired_count
            FROM applications
            WHERE client_id = %s AND status = 'accepted'
        """
        cursor.execute(hired_query, (session.get('user_id'),))
        hired_result = cursor.fetchone()
        hired_count = hired_result['hired_count'] if hired_result else 0

        # Get stats
        stats = {
            'total_hired': hired_count,
            'total_spent': total_spent,
            'posted_jobs': len(jobs),
            'total_applications': total_applications
        }

    except Exception as e:
        print(f"Error fetching client data: {e}")
        # Keep default values initialized above

    finally:
        # Ensure proper cleanup
        if 'cursor' in locals() and cursor:
            try:
                cursor.close()
            except:
                pass
        if 'conn' in locals() and conn:
            try:
                conn.close()
            except:
                pass

    return render_template('client_page.html', client=client, jobs=jobs, stats=stats, draft_jobs=draft_jobs)



@app.route('/page1')
def page1():
    # Check if we're editing an existing job
    edit_job_id = request.args.get('edit_job_id')
    reuse_job_id = request.args.get('reuse_job_id')
    draft_job_id = request.args.get('draft_job_id')

    if edit_job_id:
        try:
            # Get the job details from the database
            conn = get_db_connection()
            cursor = conn.cursor(dictionary=True, buffered=True)

            # Get job details
            query = """
                SELECT * FROM job_submissions
                WHERE id = %s AND client_id = %s
            """
            cursor.execute(query, (edit_job_id, session.get('user_id')))
            job = cursor.fetchone()

            if job:
                # Store job details in session for the job creation flow
                session['edit_job_id'] = edit_job_id
                session['job_type'] = job.get('job_type', 'One-time project')
                session['project_size'] = job.get('project_size', 'Small')
                session['title'] = job.get('title', '')
                session['job_category'] = job.get('category', '')
                session['specialty'] = job.get('specialty', '')
                session['description'] = job.get('description', '')
                session['project_description'] = job.get('project_description', '')
                session['duration'] = job.get('duration', '')
                session['experience_level'] = job.get('experience_level', '')

                session['budget_type'] = job.get('budget_type', '')
                session['budget_amount'] = job.get('budget_amount', '')
                session['skills'] = job.get('skills', '')

                # Redirect to page3 to review and edit the job
                return redirect(url_for('page3'))
        except Exception as e:
            print(f"Error fetching job for editing: {e}")
        finally:
            # Ensure proper cleanup
            if 'cursor' in locals() and cursor:
                try:
                    cursor.close()
                except:
                    pass
            if 'conn' in locals() and conn:
                try:
                    conn.close()
                except:
                    pass

    elif reuse_job_id:
        try:
            # Get the job details from the database
            conn = get_db_connection()
            cursor = conn.cursor(dictionary=True, buffered=True)

            # Get job details
            query = """
                SELECT * FROM job_submissions
                WHERE id = %s AND client_id = %s
            """
            cursor.execute(query, (reuse_job_id, session.get('user_id')))
            job = cursor.fetchone()

            if job:
                # Store job details in session for the job creation flow, but don't store the edit_job_id
                # This will create a new job based on the existing one
                session['job_type'] = job.get('job_type', 'One-time project')
                session['project_size'] = job.get('project_size', 'Small')
                session['title'] = job.get('title', '')
                session['job_category'] = job.get('category', '')
                session['specialty'] = job.get('specialty', '')
                session['description'] = job.get('description', '')
                session['project_description'] = job.get('project_description', '')
                session['duration'] = job.get('duration', '')
                session['experience_level'] = job.get('experience_level', '')

                session['budget_type'] = job.get('budget_type', '')
                session['budget_amount'] = job.get('budget_amount', '')
                session['skills'] = job.get('skills', '')

                # Redirect to page3 to review and edit the job
                return redirect(url_for('page3'))
        except Exception as e:
            print(f"Error fetching job for reuse: {e}")
        finally:
            # Ensure proper cleanup
            if 'cursor' in locals() and cursor:
                try:
                    cursor.close()
                except:
                    pass
            if 'conn' in locals() and conn:
                try:
                    conn.close()
                except:
                    pass

    elif draft_job_id:
        try:
            # Get the draft job details from the database
            conn = get_db_connection()
            cursor = conn.cursor(dictionary=True, buffered=True)

            # Get job details, ensuring it's a draft and belongs to this client
            query = """
                SELECT * FROM job_submissions
                WHERE id = %s AND client_id = %s AND status = 'draft'
            """
            cursor.execute(query, (draft_job_id, session.get('user_id')))
            job = cursor.fetchone()

            if job:
                # Store job details in session for the job creation flow
                session['edit_job_id'] = draft_job_id
                session['job_type'] = job.get('job_type', 'One-time project')
                session['project_size'] = job.get('project_size', 'Small')
                session['title'] = job.get('title', '')
                session['job_category'] = job.get('category', '')
                session['specialty'] = job.get('specialty', '')
                session['description'] = job.get('description', '')
                session['project_description'] = job.get('project_description', '')
                session['duration'] = job.get('duration', '')
                session['experience_level'] = job.get('experience_level', '')

                session['budget_type'] = job.get('budget_type', '')
                session['budget_amount'] = job.get('budget_amount', '')
                session['skills'] = job.get('skills', '')

                # Determine which page to redirect to based on what's missing in the draft
                if not job.get('description'):
                    return redirect(url_for('page2'))
                else:
                    return redirect(url_for('page3'))
            else:
                print(f"Draft job not found or not a draft: {draft_job_id}")
                return redirect(url_for('client_page'))
        except Exception as e:
            print(f"Error fetching draft job: {e}")
            return redirect(url_for('client_page'))
        finally:
            # Ensure proper cleanup
            if 'cursor' in locals() and cursor:
                try:
                    cursor.close()
                except:
                    pass
            if 'conn' in locals() and conn:
                try:
                    conn.close()
                except:
                    pass

    # Clear any previous job creation session data
    for key in ['edit_job_id', 'job_type', 'project_size', 'title', 'job_category',
                'specialty', 'description', 'project_description', 'duration',
                'experience_level', 'budget_type',
                'budget_amount', 'skills']:
        if key in session:
            session.pop(key)

    # Get draft jobs for this client to display in the dropdown
    draft_jobs = []
    try:
        conn = get_db_connection()
        cursor = conn.cursor(dictionary=True, buffered=True)

        # Get draft jobs specifically
        draft_jobs_query = """
            SELECT id, title, description, category, specialty, skills, created_at
            FROM job_submissions
            WHERE client_id = %s AND status = 'draft'
            ORDER BY created_at DESC
        """
        cursor.execute(draft_jobs_query, (session.get('user_id'),))
        draft_jobs = cursor.fetchall()

    except Exception as e:
        print(f"Error fetching draft jobs for page1: {e}")
    finally:
        # Ensure proper cleanup
        if 'cursor' in locals() and cursor:
            try:
                cursor.close()
            except:
                pass
        if 'conn' in locals() and conn:
            try:
                conn.close()
            except:
                pass

    return render_template('page1.html', draft_jobs=draft_jobs)

@app.route('/save_page1', methods=['POST'])
def save_page1():
    # Debug: Print all form data
    print("Form data received in save_page1:", request.form)

    # Get job_type from form
    job_type = request.form.get('job_type')
    print(f"Job type from form: {job_type}")

    # Store in session with a default if not provided
    if job_type:
        session['job_type'] = job_type
    else:
        session['job_type'] = 'one-time'  # Default value
        print("No job_type provided, using default: one-time")

    # Check if project_size is in the form and save it to session
    if 'project_size' in request.form:
        session['project_size'] = request.form.get('project_size')
        print(f"Project size from page1: {session['project_size']}")

    # Debug: Print session data
    print("Session data after save_page1:", {
        'job_type': session.get('job_type'),
        'project_size': session.get('project_size')
    })

    # Redirect to page2
    print("Redirecting to page2")
    return redirect(url_for('page2'))

@app.route('/page2')
def page2():
    return render_template('page2_new.html')

@app.route('/save_page2', methods=['POST'])
def save_page2():
    # Store the data in session with better error handling
    title = request.form.get('title', '')
    category = request.form.get('job_category', '')
    specialty = request.form.get('specialty', '')

    # Debug print to see what's being received
    print(f"Received from form - title: {title}, category: {category}, specialty: {specialty}")

    # Only set default if category is empty
    if not category:
        print("Warning: No category received, using default")
        category = "Writing"  # Change this to your preferred default

    session['title'] = title
    session['job_category'] = category

    # Handle specialty - save it even if empty to override any previous value
    session['specialty'] = specialty if specialty and specialty.strip() else ''
    print(f"Saved specialty to session: '{session['specialty']}'")

    print(f"Saved to session - title: {session['title']}, category: {session['job_category']}, specialty: '{session.get('specialty', 'Not set')}'")

    return redirect(url_for('page3'))

@app.route('/page3')
def page3():
    # Check if we're editing a draft job
    draft_job_id = request.args.get('draft_job_id')

    if draft_job_id:
        try:
            # Get the draft job details from the database
            conn = get_db_connection()
            cursor = conn.cursor(dictionary=True, buffered=True)

            # Get job details, ensuring it's a draft and belongs to this client
            query = """
                SELECT * FROM job_submissions
                WHERE id = %s AND client_id = %s AND status = 'draft'
            """
            cursor.execute(query, (draft_job_id, session.get('user_id')))
            job = cursor.fetchone()

            if job:
                # Store job details in session for the job creation flow
                session['edit_job_id'] = draft_job_id
                session['job_type'] = job.get('job_type', 'One-time project')
                session['project_size'] = job.get('project_size', 'Small')
                session['title'] = job.get('title', '')
                session['job_category'] = job.get('category', '')
                session['specialty'] = job.get('specialty', '')
                session['description'] = job.get('description', '')
                session['project_description'] = job.get('project_description', '')
                session['duration'] = job.get('duration', '')
                session['experience_level'] = job.get('experience_level', '')

                session['budget_type'] = job.get('budget_type', '')
                session['budget_amount'] = job.get('budget_amount', '')
                session['skills'] = job.get('skills', '')
            else:
                print(f"Draft job not found or not a draft: {draft_job_id}")
                return redirect(url_for('client_page'))
        except Exception as e:
            print(f"Error fetching draft job for page3: {e}")
            return redirect(url_for('client_page'))
        finally:
            # Ensure proper cleanup
            if 'cursor' in locals() and cursor:
                try:
                    cursor.close()
                except:
                    pass
            if 'conn' in locals() and conn:
                try:
                    conn.close()
                except:
                    pass

    # Check if session data exists for title and job_category
    if 'title' not in session or 'job_category' not in session:
        return redirect(url_for('page2'))  # Redirect to page2 if data is missing

    # Get specialty from session - don't provide a default, let template handle empty values
    specialty = session.get('specialty', '')

    # Get job_type from session if it exists, otherwise use default
    job_type = session.get('job_type', 'One-time project')

    # Pass the session data to the template for rendering on page3
    return render_template('page3.html',
                         title=session['title'],
                         job_category=session['job_category'],
                         specialty=specialty,
                         job_type=job_type,
                         is_draft_edit=bool(draft_job_id))

@app.route('/submit_job', methods=['GET', 'POST'])
@login_required
def submit_job():
    # Only clients can submit jobs
    if session.get('user_type') != 'client':
        return redirect(url_for('landing_page'))

    if request.method == 'POST':
        # Get form data
        title = request.form.get('title')
        description = request.form.get('description')
        project_size = request.form.get('project_size')
        project_description = request.form.get('project_description')
        duration = request.form.get('duration')
        experience_level = request.form.get('experience_level')

        budget_type = request.form.get('budget_type')
        budget_amount = request.form.get('budget_amount')
        category = request.form.get('category')
        specialty = request.form.get('specialty')
        skills = request.form.get('skills')
        job_type = request.form.get('job_type')
        status = request.form.get('status', 'publish')

        # Get enhanced budget data for new milestone fields
        payment_type = request.form.get('budget_subtype', 'one_time')  # Map budget_subtype to payment_type
        weekly_time_limit = request.form.get('weekly_time_limit')
        milestone_data_raw = request.form.get('milestone_data')

        # Initialize milestone fields
        milestone_count = 0
        milestone_titles = None
        milestone_deadlines = None
        milestone_payments = None

        # Parse milestone data if provided
        if milestone_data_raw:
            try:
                import json
                milestone_data = json.loads(milestone_data_raw)

                if milestone_data and 'milestones' in milestone_data:
                    milestones = milestone_data['milestones']
                    milestone_count = len(milestones)

                    # Extract titles, deadlines, and payments into separate arrays
                    titles = []
                    deadlines = []
                    payments = []

                    for milestone in milestones:
                        titles.append(milestone.get('title', ''))
                        deadlines.append(milestone.get('deadline', ''))
                        payments.append(float(milestone.get('payment', 0)))

                    # Convert to JSON strings for database storage
                    milestone_titles = json.dumps(titles)
                    milestone_deadlines = json.dumps(deadlines)
                    milestone_payments = json.dumps(payments)

                    # Set payment type to milestone
                    payment_type = 'milestone'

            except (json.JSONDecodeError, TypeError, ValueError) as e:
                print(f"Error parsing milestone data: {milestone_data_raw}, Error: {e}")

        # Set payment type based on budget type if not milestone
        if payment_type == 'one_time' and budget_type:
            if budget_type == 'hourly':
                payment_type = 'hourly'
            elif budget_type == 'fixed':
                payment_type = 'one_time'

        # Debug print to see what data is being received
        print(f"DEBUG: Received skills: '{skills}' (type: {type(skills)})")
        print(f"DEBUG: Payment type: {payment_type}, Milestone count: {milestone_count}")
        print(f"DEBUG: Weekly time limit: {weekly_time_limit}")
        print(f"DEBUG: Milestone data raw: {milestone_data_raw}")
        print(f"DEBUG: Milestone titles: {milestone_titles}")
        print(f"DEBUG: Milestone deadlines: {milestone_deadlines}")
        print(f"DEBUG: Milestone payments: {milestone_payments}")

        # Get client information
        try:
            conn = get_db_connection()
            cursor = conn.cursor(dictionary=True, buffered=True)

            # Get client details
            client_query = """
                SELECT first_name, last_name, country, position, profile_photo
                FROM approve_client
                WHERE id = %s
            """
            cursor.execute(client_query, (session.get('user_id'),))
            client_info = cursor.fetchone()

            if not client_info:
                return jsonify(success=False, error="Client information not found")

            first_name = client_info['first_name']
            last_name = client_info['last_name']
            country = client_info['country']
            position = client_info['position']
            profile_photo = client_info['profile_photo']

            # Validate required fields
            if status == 'publish':
                if not title or not description:
                    return jsonify(success=False, error="Title and description are required")
            else:
                # For draft, only title is required
                if not title:
                    return jsonify(success=False, error="Title is required even for drafts")

            # Check if we're editing an existing job
            edit_job_id = session.get('edit_job_id')

            if edit_job_id:
                # Update existing job
                update_query = """
                    UPDATE job_submissions SET
                        title = %s, description = %s, project_size = %s,
                        project_description = %s, duration = %s, experience_level = %s,
                        budget_type = %s, payment_type = %s, budget_amount = %s,
                        milestone_count = %s, weekly_time_limit = %s, milestone_titles = %s,
                        milestone_deadlines = %s, milestone_payments = %s,
                        category = %s, specialty = %s, skills = %s, job_type = %s,
                        status = %s, updated_at = NOW()
                    WHERE id = %s AND client_id = %s
                """
                cursor.execute(update_query, (
                    title, description, project_size, project_description, duration,
                    experience_level, budget_type, payment_type, budget_amount,
                    milestone_count, weekly_time_limit, milestone_titles,
                    milestone_deadlines, milestone_payments,
                    category, specialty, skills, job_type, status, edit_job_id, session.get('user_id')
                ))
                conn.commit()
                job_id = edit_job_id

                # Clear the edit_job_id from session
                session.pop('edit_job_id', None)

                print(f"Updated job with ID: {job_id}")
            else:
                # Insert new job
                query = """
                    INSERT INTO job_submissions (
                        client_id, first_name, last_name, country, position, profile_photo,
                        title, description, project_size, project_description, duration,
                        experience_level, budget_type, payment_type, budget_amount,
                        milestone_count, weekly_time_limit, milestone_titles, milestone_deadlines, milestone_payments,
                        category, specialty, skills, job_type, status, created_at, updated_at
                    ) VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, NOW(), NOW())
                """
                cursor.execute(query, (
                    session.get('user_id'), first_name, last_name, country, position, profile_photo,
                    title, description, project_size, project_description, duration,
                    experience_level, budget_type, payment_type, budget_amount,
                    milestone_count, weekly_time_limit, milestone_titles, milestone_deadlines, milestone_payments,
                    category, specialty, skills, job_type, status
                ))
                conn.commit()
                job_id = cursor.lastrowid

            # Return success response with redirect to client_page
            return jsonify(success=True, job_id=job_id, redirect=url_for('client_page'))

        except Exception as e:
            print(f"Error submitting job: {e}")
            return jsonify(success=False, error="An error occurred while submitting the job")
        finally:
            # Ensure proper cleanup
            if 'cursor' in locals() and cursor:
                try:
                    cursor.close()
                except:
                    pass
            if 'conn' in locals() and conn:
                try:
                    conn.close()
                except:
                    pass

    # GET request - render the job submission form with the JavaScript file
    return render_template('submit_job.html', js_file='submit_job.js')

@app.route('/all_contracts')
@login_required
def all_contracts():
    """Route to display all contracts page - CLIENT ONLY"""
    # Only clients can access contracts
    if session.get('user_type') != 'client':
        return redirect(url_for('landing_page'))

    return render_template('all_contracts.html')

@app.route('/your_hires')
@login_required
def your_hires():
    """Route to display your hires page - CLIENT ONLY"""
    # Only clients can access your hires
    if session.get('user_type') != 'client':
        return redirect(url_for('landing_page'))

    return render_template('your_hires.html')

@app.route('/test_milestone_data/<int:job_id>')
@login_required
def test_milestone_data(job_id):
    """Test route to demonstrate milestone data storage and retrieval"""
    if session.get('user_type') != 'client':
        return redirect(url_for('landing_page'))

    try:
        conn = get_db_connection()
        cursor = conn.cursor(dictionary=True)

        # Get the job with milestone data
        cursor.execute("""
            SELECT id, title, budget_type, payment_type, budget_amount,
                   milestone_count, weekly_time_limit, milestone_titles,
                   milestone_deadlines, milestone_payments
            FROM job_submissions
            WHERE id = %s AND client_id = %s
        """, (job_id, session.get('user_id')))
        job = cursor.fetchone()

        if job:
            import json
            result = f"""
            <h2>Job: {job['title']}</h2>
            <p><strong>Budget Type:</strong> {job['budget_type']}</p>
            <p><strong>Payment Type:</strong> {job['payment_type']}</p>
            <p><strong>Budget Amount:</strong> ${job['budget_amount']}</p>
            <p><strong>Milestone Count:</strong> {job['milestone_count']}</p>
            """

            if job['weekly_time_limit']:
                result += f"<p><strong>Weekly Time Limit:</strong> {job['weekly_time_limit']} hours</p>"

            # Display milestone data if available
            if job['milestone_count'] > 0 and job['milestone_titles']:
                try:
                    titles = json.loads(job['milestone_titles'])
                    deadlines = json.loads(job['milestone_deadlines']) if job['milestone_deadlines'] else []
                    payments = json.loads(job['milestone_payments']) if job['milestone_payments'] else []

                    result += "<h3>Milestone Breakdown:</h3><ul>"

                    for i in range(len(titles)):
                        title = titles[i] if i < len(titles) else 'N/A'
                        deadline = deadlines[i] if i < len(deadlines) else 'N/A'
                        payment = payments[i] if i < len(payments) else 0

                        result += f"""
                        <li>
                            <strong>Milestone {i+1}:</strong> {title}<br>
                            <strong>Deadline:</strong> {deadline}<br>
                            <strong>Payment:</strong> ${payment}<br>
                        </li>
                        """
                    result += "</ul>"
                except json.JSONDecodeError:
                    result += "<p>Error parsing milestone data</p>"
            else:
                result += "<p>No milestone data found</p>"

            return result
        else:
            return "Job not found or not owned by you"

    except Exception as e:
        return f"Error: {e}"
    finally:
        if 'cursor' in locals():
            cursor.close()
        if 'conn' in locals():
            conn.close()

@app.route('/fix_job_skills/<int:job_id>')
@login_required
def fix_job_skills(job_id):
    """Temporary route to add skills to existing jobs for testing"""
    if session.get('user_type') != 'client':
        return redirect(url_for('landing_page'))

    try:
        conn = get_db_connection()
        cursor = conn.cursor()

        # Get the job to see what category it is
        cursor.execute("SELECT category, specialty FROM job_submissions WHERE id = %s AND client_id = %s",
                      (job_id, session.get('user_id')))
        job = cursor.fetchone()

        if job:
            category = job['category'] if job['category'] else 'Web Development'

            # Add some relevant skills based on category
            if 'Web' in category or 'Software' in category:
                skills = '["JavaScript", "HTML", "CSS", "React", "Node.js", "Python"]'
            elif 'Design' in category:
                skills = '["Photoshop", "Illustrator", "Figma", "UI/UX Design", "Graphic Design"]'
            elif 'Writing' in category:
                skills = '["Content Writing", "SEO", "Copywriting", "Research", "Editing"]'
            elif 'Marketing' in category:
                skills = '["Digital Marketing", "Social Media", "SEO", "Google Ads", "Analytics"]'
            else:
                skills = '["Communication", "Problem Solving", "Time Management", "Research"]'

            # Update the job with skills
            cursor.execute("UPDATE job_submissions SET skills = %s WHERE id = %s AND client_id = %s",
                          (skills, job_id, session.get('user_id')))
            conn.commit()

            return f"Skills added to job {job_id}: {skills}"
        else:
            return "Job not found or not owned by you"

    except Exception as e:
        return f"Error: {e}"
    finally:
        if 'cursor' in locals():
            cursor.close()
        if 'conn' in locals():
            conn.close()

@app.route('/apply_for_job', methods=['POST'])
@login_required
def apply_for_job():
    # Only geniuses can apply for jobs
    if session.get('user_type') != 'genius':
        return jsonify(success=False, error="Only geniuses can apply for jobs")

    # Get job ID from request
    data = request.get_json()
    job_id = data.get('job_id')

    print(f"Received application request for job ID: {job_id} from genius ID: {session.get('user_id')}")

    if not job_id:
        return jsonify(success=False, error="Job ID is required")

    try:
        # Get a connection
        conn = get_db_connection()
        cursor = conn.cursor()

        # Check if applications table exists
        try:
            check_table_query = """
                SELECT COUNT(*)
                FROM information_schema.tables
                WHERE table_schema = 'giggenius'
                AND table_name = 'applications'
            """
            cursor.execute(check_table_query)
            table_exists = cursor.fetchone()[0] > 0

            # If table doesn't exist, create it
            if not table_exists:
                print("applications table does not exist, creating it now...")
                create_table_query = """
                    CREATE TABLE applications (
                        id INT AUTO_INCREMENT PRIMARY KEY,
                        genius_id INT NOT NULL,
                        client_id INT NULL,
                        job_id INT NOT NULL,
                        first_name VARCHAR(100) NULL,
                        last_name VARCHAR(100) NULL,
                        profile_photo VARCHAR(255) NULL,
                        position VARCHAR(100) NULL,
                        status VARCHAR(50) NOT NULL,
                        created_at DATETIME NOT NULL,
                        updated_at DATETIME NULL,
                        INDEX (genius_id),
                        INDEX (job_id),
                        INDEX (client_id)
                    )
                """
                cursor.execute(create_table_query)
                conn.commit()
                print("Successfully created applications table")
        except Exception as e:
            print(f"Error checking/creating applications table: {e}")
            # Try to create the table anyway
            try:
                create_table_query = """
                    CREATE TABLE IF NOT EXISTS applications (
                        id INT AUTO_INCREMENT PRIMARY KEY,
                        genius_id INT NOT NULL,
                        client_id INT NULL,
                        job_id INT NOT NULL,
                        first_name VARCHAR(100) NULL,
                        last_name VARCHAR(100) NULL,
                        profile_photo VARCHAR(255) NULL,
                        position VARCHAR(100) NULL,
                        status VARCHAR(50) NOT NULL,
                        created_at DATETIME NOT NULL,
                        updated_at DATETIME NULL,
                        INDEX (genius_id),
                        INDEX (job_id),
                        INDEX (client_id)
                    )
                """
                cursor.execute(create_table_query)
                conn.commit()
                print("Created applications table as fallback")
            except Exception as e2:
                print(f"Failed to create applications table: {e2}")
                return jsonify(success=False, error="Database error. Please try again.")

        # Check if already applied
        check_query = """
            SELECT id FROM applications
            WHERE genius_id = %s AND job_id = %s
        """
        cursor.execute(check_query, (session.get('user_id'), job_id))
        existing_application = cursor.fetchone()

        if existing_application:
            cursor.close()
            conn.close()
            return jsonify(success=False, error="You have already applied for this job")

        # Insert new application
        try:
            print(f"Inserting application for genius_id={session.get('user_id')}, job_id={job_id}")

            # First, get the client_id from the job_submissions table
            client_query = """
                SELECT client_id FROM job_submissions WHERE id = %s
            """
            cursor.execute(client_query, (job_id,))
            client_result = cursor.fetchone()
            client_id = client_result[0] if client_result else None

            # Get genius information
            genius_query = """
                SELECT first_name, last_name, profile_photo, position
                FROM approve_genius
                WHERE id = %s
            """
            cursor.execute(genius_query, (session.get('user_id'),))
            genius_info = cursor.fetchone()

            first_name = genius_info[0] if genius_info and genius_info[0] else session.get('first_name')
            last_name = genius_info[1] if genius_info and genius_info[1] else session.get('last_name')
            profile_photo = genius_info[2] if genius_info and len(genius_info) > 2 else None
            position = genius_info[3] if genius_info and len(genius_info) > 3 else None

            # Insert the application
            query = """
                INSERT INTO applications (
                    genius_id, client_id, job_id, first_name, last_name,
                    profile_photo, position, status, created_at, updated_at
                ) VALUES (%s, %s, %s, %s, %s, %s, %s, %s, NOW(), NOW())
            """
            cursor.execute(query, (
                session.get('user_id'),
                client_id,
                job_id,
                first_name,
                last_name,
                profile_photo,
                position,
                'pending'
            ))
            conn.commit()
            application_id = cursor.lastrowid
            print(f"Successfully inserted application with ID: {application_id}")

            # Get client information for the response
            client_query = """
                SELECT ac.first_name, ac.last_name, ac.business_name
                FROM job_submissions js
                JOIN approve_client ac ON js.client_id = ac.id
                WHERE js.id = %s
            """
            cursor.execute(client_query, (job_id,))
            client_info = cursor.fetchone()

            client_name = "Client"
            if client_info:
                if client_info[2]:  # business_name
                    client_name = client_info[2]
                elif client_info[0] and client_info[1]:  # first_name and last_name
                    client_name = f"{client_info[0]} {client_info[1]}"

            cursor.close()
            conn.close()

            # Return success response with client info for the automatic message
            return jsonify(
                success=True,
                message="Application submitted successfully",
                application_id=application_id,
                client_name=client_name,
                auto_message="Thank you for your application. I look forward to working with you! 👋"
            )
        except Exception as e:
            print(f"Error inserting application: {e}")
            try:
                # Try one more time with explicit values and minimal fields
                query = """
                    INSERT INTO applications (
                        genius_id, job_id, status, created_at, updated_at
                    ) VALUES (%s, %s, %s, NOW(), NOW())
                """
                genius_id = int(session.get('user_id'))
                job_id_int = int(job_id)
                cursor.execute(query, (genius_id, job_id_int, 'pending'))
                conn.commit()
                application_id = cursor.lastrowid
                print(f"Successfully inserted application on second attempt with ID: {application_id}")

                # Try to get client information
                try:
                    client_query = """
                        SELECT ac.first_name, ac.last_name, ac.business_name
                        FROM job_submissions js
                        JOIN approve_client ac ON js.client_id = ac.id
                        WHERE js.id = %s
                    """
                    cursor.execute(client_query, (job_id_int,))
                    client_info = cursor.fetchone()

                    client_name = "Client"
                    if client_info:
                        if client_info[2]:  # business_name
                            client_name = client_info[2]
                        elif client_info[0] and client_info[1]:  # first_name and last_name
                            client_name = f"{client_info[0]} {client_info[1]}"
                except Exception as e:
                    print(f"Error getting client info: {e}")
                    client_name = "Client"

                cursor.close()
                conn.close()

                return jsonify(
                    success=True,
                    message="Application submitted successfully",
                    application_id=application_id,
                    client_name=client_name,
                    auto_message="Thank you for your application. I look forward to working with you! 👋"
                )
            except Exception as e2:
                print(f"Error inserting application on second attempt: {e2}")
                cursor.close()
                conn.close()
                return jsonify(success=False, error="Failed to submit application. Please try again.")

    except Exception as e:
        print(f"Error applying for job: {e}")
        return jsonify(success=False, error="An error occurred while submitting your application")


@app.route('/genius_profile')
@login_required
def genius_profile():
    if session.get('user_type') != 'genius':
        return redirect(url_for('landing_page'))

    genius = {
        'id': session.get('user_id'),
        'first_name': session.get('first_name', 'User'),
        'last_name': session.get('last_name', ''),
        'email': session.get('email', ''),
        'profile_picture_url': '/static/img/default-avatar.png',
        'position': 'Freelancer',
        'hourly_rate': 0,
        'country': 'Not specified',
        'introduction': '',
        'professional_sum': '',
        'project_title': '',
        'project_role': '',
        'skills_and_deliverables': '',
        'related_giggenius_job': ''
    }
    try:
        conn = get_db_connection()
        cursor = conn.cursor(dictionary=True, buffered=True)
        genius_query = """
            SELECT id, first_name, last_name, email, profile_photo, position, hourly_rate, country, introduction, professional_sum, mobile, expertise, availability, language,
                   project_title, project_role, project_description, project_content, skills_and_deliverables, related_giggenius_job
            FROM approve_genius
            WHERE id = %s
        """
        cursor.execute(genius_query, (session.get('user_id'),))
        genius_data = cursor.fetchone()
        if genius_data:
            genius['first_name'] = genius_data['first_name'] or genius['first_name']
            genius['last_name'] = genius_data['last_name'] or genius['last_name']
            genius['email'] = genius_data['email'] or genius['email']
            if genius_data['profile_photo']:
                genius['profile_picture_url'] = genius_data['profile_photo']
            if genius_data['position']:
                genius['position'] = genius_data['position']
            if genius_data['hourly_rate']:
                genius['hourly_rate'] = genius_data['hourly_rate']
            if genius_data['country']:
                genius['country'] = genius_data['country']
            if genius_data.get('introduction'):
                genius['introduction'] = genius_data['introduction']
            else:
                genius['introduction'] = ''
            if genius_data.get('professional_sum'):
                genius['professional_sum'] = genius_data['professional_sum']
            else:
                genius['professional_sum'] = ''
            if genius_data.get('mobile'):
                genius['mobile'] = genius_data['mobile']
            else:
                genius['mobile'] = ''
            if genius_data.get('expertise'):
                genius['expertise'] = genius_data['expertise']
            else:
                genius['expertise'] = ''
            if genius_data.get('availability'):
                genius['availability'] = genius_data['availability']
            else:
                genius['availability'] = ''
            if genius_data.get('language'):
                genius['language'] = genius_data['language']
            else:
                genius['language'] = ''

            # Portfolio fields
            if genius_data.get('project_title'):
                genius['project_title'] = genius_data['project_title']
            else:
                genius['project_title'] = ''

            if genius_data.get('project_role'):
                genius['project_role'] = genius_data['project_role']
            else:
                genius['project_role'] = '' 

            if genius_data.get('skills_and_deliverables'):
                genius['skills_and_deliverables'] = genius_data['skills_and_deliverables']
            else:
                genius['skills_and_deliverables'] = ''

            if genius_data.get('related_giggenius_job'):
                genius['related_giggenius_job'] = genius_data['related_giggenius_job']
            else:
                genius['related_giggenius_job'] = ''

        # Load project data from approve_genius table
        try:
            # Get all project fields from approve_genius table
            project_query = """
                SELECT project_title, project_role, project_description, project_content, skills_and_deliverables, related_giggenius_job
                FROM approve_genius
                WHERE id = %s
            """
            cursor.execute(project_query, (session.get('user_id'),))
            result = cursor.fetchone()

            project_title = result['project_title'] if result and result['project_title'] else None
            project_role = result['project_role'] if result and result['project_role'] else None
            project_description = result['project_description'] if result and result['project_description'] else None
            project_content = result['project_content'] if result and result['project_content'] else None
            skills_and_deliverables = result['skills_and_deliverables'] if result and result['skills_and_deliverables'] else None
            related_giggenius_job = result['related_giggenius_job'] if result and result['related_giggenius_job'] else None
            print(f"Project from approve_genius - Title: {project_title}, Role: {project_role}, Description: {project_description}, Content: {project_content}, Skills: {skills_and_deliverables}, Related Job: {related_giggenius_job}")

            # Create simple portfolio structure
            if project_title and project_title.strip():
                # If there's a project title, show it in published
                published_projects = [{
                    'id': 1,
                    'project_title': project_title,
                    'project_role': project_role if project_role and project_role.strip() else 'Project Role',
                    'project_description': project_description if project_description and project_description.strip() else '',
                    'project_content': project_content if project_content and project_content.strip() else '',
                    'skills_and_deliverables': skills_and_deliverables if skills_and_deliverables and skills_and_deliverables.strip() else '',
                    'related_giggenius_job': related_giggenius_job if related_giggenius_job and related_giggenius_job.strip() else '',
                    'status': 'published',
                    'project_image_filename': None,
                    'project_image_mimetype': None,
                    'created_at': None,
                    'updated_at': None
                }]
                draft_projects = []
            else:
                # No project title yet
                published_projects = []
                draft_projects = []

            genius['portfolio'] = {
                'published': published_projects,
                'drafts': draft_projects,
                'all': published_projects + draft_projects
            }
        except Exception as e:
            print(f"Error loading project data: {e}")
            genius['portfolio'] = {'published': [], 'drafts': [], 'all': []}

        cursor.close()
        conn.close()
    except Exception as e:
        print(f"Error fetching genius profile: {e}")
    return render_template('Genius_Profile.html', genius=genius)

@app.route('/tax_info')
@login_required
def tax_info():
    """
    Tax information page for genius users to manage their tax details
    """
    # Only geniuses can access this page
    if session.get('user_type') != 'genius':
        return redirect(url_for('landing_page'))

    # Render the Tax_info.html template
    return render_template('Tax_info.html')

@app.route('/withdraw_earnings')
@login_required
def withdraw_earnings():
    """
    Withdraw earnings page for genius users to withdraw their earnings
    """
    # Only geniuses can access this page
    if session.get('user_type') != 'genius':
        return redirect(url_for('landing_page'))

    # Render the Withdraw_Earnings.html template
    return render_template('Withdraw_Earnings.html')

@app.route('/billing_and_earnings')
@login_required
def billing_and_earnings():
    """
    Billing and earnings page for genius users to view their financial information
    """
    # Only geniuses can access this page
    if session.get('user_type') != 'genius':
        return redirect(url_for('landing_page'))

    # Render the Billing_and_Earnings.html template
    return render_template('Billing_and_Earnings.html')

@app.route('/tracker')
@login_required
def tracker():
    """
    Work tracker page for genius users to log their work hours
    """
    # Only geniuses can access this page
    if session.get('user_type') != 'genius':
        return redirect(url_for('landing_page'))

    # Render the Tracker.html template
    return render_template('Tracker.html')

@app.route('/api/tracker/accepted_jobs')
@login_required
def get_accepted_jobs_for_tracker():
    """
    API endpoint to fetch accepted job applications for the tracker
    """
    # Only geniuses can access this endpoint
    if session.get('user_type') != 'genius':
        return jsonify(success=False, error="Access denied")

    try:
        conn = get_db_connection()
        cursor = conn.cursor(dictionary=True)

        # Check if applications table exists
        cursor.execute("SHOW TABLES LIKE 'applications'")
        table_exists = cursor.fetchone() is not None

        if not table_exists:
            return jsonify(success=True, jobs=[])

        # Get accepted applications for the current genius with job details
        query = """
            SELECT
                a.id as application_id,
                a.job_id,
                a.status,
                a.created_at as start_date,
                js.title,
                js.first_name,
                js.last_name,
                js.weekly_time_limit,
                js.budget_amount,
                js.budget_type,
                js.payment_type,
                js.duration,
                js.category,
                js.specialty,
                ac.business_name as client_company,
                CONCAT(ac.first_name, ' ', ac.last_name) as client_contact
            FROM applications a
            JOIN job_submissions js ON a.job_id = js.id
            JOIN approve_client ac ON js.client_id = ac.id
            WHERE a.genius_id = %s
            AND (LOWER(a.status) IN ('accepted', 'accept', 'active') OR a.status IN ('ACCEPTED', 'ACCEPT', 'ACTIVE'))
            ORDER BY a.created_at DESC
        """

        cursor.execute(query, (session.get('user_id'),))
        jobs = cursor.fetchall()

        # Format the data for the frontend
        formatted_jobs = []
        for job in jobs:
            try:
                # Determine job type based on payment structure
                payment_type = job.get('payment_type', '').lower()
                budget_type = job.get('budget_type', '').lower()

                if payment_type == 'hourly' or budget_type == 'hourly':
                    job_type = 'time-tracking'
                elif payment_type == 'milestone' or budget_type == 'milestone':
                    job_type = 'milestone'
                elif payment_type == 'fixed' or budget_type == 'fixed' or payment_type == 'one-time':
                    job_type = 'one-time'
                else:
                    # Default based on budget amount - if it's a single amount, likely one-time
                    job_type = 'one-time'

                # Calculate rate for hourly jobs
                if job_type == 'time-tracking' and job.get('budget_amount'):
                    try:
                        budget_amount = float(str(job.get('budget_amount')).replace('$', '').replace(',', ''))
                        weekly_hours = job.get('weekly_time_limit') or 40
                        rate = budget_amount / weekly_hours if weekly_hours > 0 else 25
                    except (ValueError, TypeError):
                        rate = 25
                else:
                    rate = 25  # Default rate

                formatted_job = {
                    'id': job.get('job_id'),
                    'application_id': job.get('application_id'),
                    'type': job_type,
                    'title': job.get('title', 'Untitled Job'),
                    'client': job.get('client_company') or f"{job.get('first_name', '')} {job.get('last_name', '')}".strip() or 'Client',
                    'clientContact': job.get('client_contact', 'Contact'),
                    'startDate': job['start_date'].strftime('%b %d, %Y') if job.get('start_date') else '',
                    'status': 'Active',
                    'rate': rate,
                    'contractHours': job.get('weekly_time_limit') or 40,
                    'totalHours': 0,  # Will be calculated from time logs
                    'totalEarnings': 0,  # Will be calculated from time logs
                    'totalContract': float(str(job.get('budget_amount', 0)).replace('$', '').replace(',', '')) if job.get('budget_amount') else 0,
                    'budgetType': job.get('budget_type'),
                    'paymentType': job.get('payment_type'),
                    'duration': job.get('duration'),
                    'category': job.get('category'),
                    'specialty': job.get('specialty'),
                    'timeLogs': []  # Will be populated from time tracking table
                }
                formatted_jobs.append(formatted_job)
            except Exception as e:
                print(f"Error formatting job {job}: {e}")
                continue

        cursor.close()
        conn.close()

        return jsonify(success=True, jobs=formatted_jobs)

    except Exception as e:
        print(f"Error fetching accepted jobs for tracker: {e}")
        return jsonify(success=False, error=str(e))

@app.route('/api/tracker/create_time_logs_table')
@login_required
def create_time_logs_table():
    """
    Create the time_logs table for work tracking
    """
    # Only geniuses can access this endpoint
    if session.get('user_type') != 'genius':
        return jsonify(success=False, error="Access denied")

    try:
        conn = get_db_connection()
        cursor = conn.cursor()

        # Check if time_logs table exists
        cursor.execute("SHOW TABLES LIKE 'time_logs'")
        table_exists = cursor.fetchone() is not None

        if not table_exists:
            print("time_logs table does not exist, creating it now...")
            create_table_query = """
                CREATE TABLE time_logs (
                    id INT AUTO_INCREMENT PRIMARY KEY,
                    genius_id INT NOT NULL,
                    job_id INT NOT NULL,
                    application_id INT NOT NULL,
                    date DATE NOT NULL,
                    time_in TIME NULL,
                    time_out TIME NULL,
                    duration TIME NOT NULL,
                    is_manual BOOLEAN DEFAULT FALSE,
                    notes TEXT NULL,
                    proof_filename VARCHAR(255) NULL,
                    proof_data LONGBLOB NULL,
                    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                    INDEX (genius_id),
                    INDEX (job_id),
                    INDEX (application_id),
                    INDEX (date)
                )
            """
            cursor.execute(create_table_query)
            conn.commit()
            print("Successfully created time_logs table")

        cursor.close()
        conn.close()

        return jsonify(success=True, message="Time logs table ready")

    except Exception as e:
        print(f"Error creating time_logs table: {e}")
        return jsonify(success=False, error=str(e))

@app.route('/api/tracker/time_logs/<int:job_id>')
@login_required
def get_time_logs(job_id):
    """
    Get time logs for a specific job from both approve_genius and time_logs tables
    """
    # Only geniuses can access this endpoint
    if session.get('user_type') != 'genius':
        return jsonify(success=False, error="Access denied")

    try:
        conn = get_db_connection()
        cursor = conn.cursor(dictionary=True)

        formatted_logs = []

        # Get current session data from approve_genius table
        genius_query = """
            SELECT date, time_in, time_out, duration, duration_hours, type, time_of_day,
                   session_notes, last_session_date, job_id
            FROM approve_genius
            WHERE id = %s AND job_id = %s
        """
        cursor.execute(genius_query, (session.get('user_id'), job_id))
        current_session = cursor.fetchone()

        if current_session and current_session['date']:
            # Add current session from approve_genius
            formatted_log = {
                'id': 'current',
                'date': current_session['date'].strftime('%Y-%m-%d') if current_session['date'] else '',
                'timeIn': str(current_session['time_in']) if current_session['time_in'] else '',
                'timeOut': str(current_session['time_out']) if current_session['time_out'] else '',
                'duration': current_session['duration'] or '',
                'durationHours': float(current_session['duration_hours']) if current_session['duration_hours'] else 0.0,
                'isManual': current_session['type'] == 'Manual',
                'notes': current_session['session_notes'] or '',
                'timeOfDay': current_session['time_of_day'] or '',
                'sessionType': current_session['type'] or '',
                'createdAt': current_session['last_session_date'].strftime('%Y-%m-%d %H:%M:%S') if current_session['last_session_date'] else ''
            }
            formatted_logs.append(formatted_log)

        # Get historical data from time_logs table if it exists
        cursor.execute("SHOW TABLES LIKE 'time_logs'")
        table_exists = cursor.fetchone() is not None

        if table_exists:
            # Get time logs for the job
            query = """
                SELECT id, date, time_in, time_out, duration, is_manual, notes, created_at
                FROM time_logs
                WHERE genius_id = %s AND job_id = %s
                ORDER BY date DESC, created_at DESC
            """
            cursor.execute(query, (session.get('user_id'), job_id))
            logs = cursor.fetchall()

            # Format historical data - SHOW ALL TIME LOG ENTRIES
            print(f"📋 Found {len(logs)} time log entries for job {job_id}")

            for log in logs:
                # Determine time of day based on time_in
                time_of_day = ''
                if log['time_in']:
                    time_str = str(log['time_in'])
                    if ':' in time_str:
                        hour = int(time_str.split(':')[0])
                        if 5 <= hour < 12:
                            time_of_day = 'Morning'
                        elif 12 <= hour < 17:
                            time_of_day = 'Afternoon'
                        else:
                            time_of_day = 'Evening'

                # Calculate duration in hours for display
                duration_hours = 0.0
                if log['duration']:
                    try:
                        duration_str = str(log['duration'])
                        if ':' in duration_str:
                            parts = duration_str.split(':')
                            hours = float(parts[0])
                            minutes = float(parts[1]) if len(parts) > 1 else 0
                            seconds = float(parts[2]) if len(parts) > 2 else 0
                            duration_hours = hours + (minutes / 60.0) + (seconds / 3600.0)
                    except:
                        duration_hours = 0.0

                formatted_log = {
                    'id': f"log_{log['id']}",  # Prefix to distinguish from current session
                    'date': log['date'].strftime('%Y-%m-%d') if log['date'] else '',
                    'timeIn': str(log['time_in']) if log['time_in'] else '',
                    'timeOut': str(log['time_out']) if log['time_out'] else '',
                    'duration': str(log['duration']) if log['duration'] else '',
                    'durationHours': duration_hours,
                    'isManual': bool(log['is_manual']),
                    'notes': log['notes'] or '',
                    'timeOfDay': time_of_day,
                    'sessionType': 'Manual' if log['is_manual'] else 'Timer',
                    'createdAt': log['created_at'].strftime('%Y-%m-%d %H:%M:%S') if log['created_at'] else ''
                }
                formatted_logs.append(formatted_log)
                print(f"  📝 Log {log['id']}: {log['date']} {log['time_in']}-{log['time_out']} ({log['duration']}) - {'Manual' if log['is_manual'] else 'Timer'}")

        cursor.close()
        conn.close()

        return jsonify(success=True, time_logs=formatted_logs)

    except Exception as e:
        print(f"Error fetching time logs: {e}")
        return jsonify(success=False, error=str(e))

@app.route('/api/tracker/setup_time_tracking', methods=['POST'])
@login_required
def setup_time_tracking():
    """
    Add time tracking columns to approve_genius table
    """
    # Only geniuses can access this endpoint
    if session.get('user_type') != 'genius':
        return jsonify(success=False, error="Access denied")

    try:
        conn = get_db_connection()
        cursor = conn.cursor()

        # Check and add time tracking columns to approve_genius table
        time_tracking_columns = [
            ('date', 'DATE NULL'),
            ('time_in', 'VARCHAR(20) NULL'),
            ('time_out', 'VARCHAR(20) NULL'),
            ('duration', 'VARCHAR(20) NULL'),
            ('duration_hours', 'DECIMAL(5,2) DEFAULT 0.00'),
            ('type', 'VARCHAR(20) NULL'),
            ('time_of_day', 'VARCHAR(20) NULL'),
            ('job_id', 'INT NULL'),
            ('application_id', 'INT NULL'),
            ('session_notes', 'TEXT NULL'),
            ('last_session_date', 'DATETIME NULL')
        ]

        for column_name, column_definition in time_tracking_columns:
            # Check if column exists
            cursor.execute(f"SHOW COLUMNS FROM approve_genius LIKE '{column_name}'")
            column_info = cursor.fetchone()
            column_exists = column_info is not None

            if not column_exists:
                print(f"Adding {column_name} column to approve_genius table...")
                alter_query = f"""
                    ALTER TABLE approve_genius
                    ADD COLUMN {column_name} {column_definition}
                """
                cursor.execute(alter_query)
                conn.commit()
                print(f"{column_name} column added successfully")
            else:
                # Check if we need to modify existing column type
                if column_name in ['time_in', 'time_out'] and column_info:
                    current_type = column_info[1].upper()
                    if 'TIME' in current_type and 'VARCHAR' not in current_type:
                        print(f"Modifying {column_name} column type from {current_type} to VARCHAR(20)...")
                        modify_query = f"""
                            ALTER TABLE approve_genius
                            MODIFY COLUMN {column_name} VARCHAR(20) NULL
                        """
                        cursor.execute(modify_query)
                        conn.commit()
                        print(f"{column_name} column type modified successfully")
                elif column_name == 'duration' and column_info:
                    current_type = column_info[1].upper()
                    if 'VARCHAR(20)' not in current_type:
                        print(f"Modifying {column_name} column type from {current_type} to VARCHAR(20)...")
                        modify_query = f"""
                            ALTER TABLE approve_genius
                            MODIFY COLUMN {column_name} VARCHAR(20) NULL
                        """
                        cursor.execute(modify_query)
                        conn.commit()
                        print(f"{column_name} column type modified successfully")
                elif column_name == 'duration_hours' and column_info:
                    current_type = column_info[1].upper()
                    if 'DEFAULT' not in current_type:
                        print(f"Modifying {column_name} column to add default value...")
                        modify_query = f"""
                            ALTER TABLE approve_genius
                            MODIFY COLUMN {column_name} DECIMAL(5,2) DEFAULT 0.00
                        """
                        cursor.execute(modify_query)
                        conn.commit()
                        print(f"{column_name} column default value added successfully")

        cursor.close()
        conn.close()

        return jsonify(success=True, message="Time tracking setup completed")

    except Exception as e:
        print(f"Error setting up time tracking: {e}")
        return jsonify(success=False, error=str(e))



@app.route('/api/tracker/setup_file_storage', methods=['POST'])
@login_required
def setup_tracker_file_storage():
    """
    Add tracker_upload_files column to approve_genius table and create tracker_files table
    """
    # Only geniuses can access this endpoint
    if session.get('user_type') != 'genius':
        return jsonify(success=False, error="Access denied")

    try:
        conn = get_db_connection()
        cursor = conn.cursor()

        # Check if tracker_upload_files column exists in approve_genius table
        cursor.execute("SHOW COLUMNS FROM approve_genius LIKE 'tracker_upload_files'")
        column_exists = cursor.fetchone() is not None

        if not column_exists:
            print("Adding tracker_upload_files column to approve_genius table...")
            alter_query = """
                ALTER TABLE approve_genius
                ADD COLUMN tracker_upload_files LONGTEXT NULL
            """
            cursor.execute(alter_query)
            conn.commit()
            print("tracker_upload_files column added successfully")

        # Check if tracker_files table exists
        cursor.execute("SHOW TABLES LIKE 'tracker_files'")
        table_exists = cursor.fetchone() is not None

        if not table_exists:
            print("Creating tracker_files table...")
            create_table_query = """
                CREATE TABLE tracker_files (
                    id INT AUTO_INCREMENT PRIMARY KEY,
                    genius_id INT NOT NULL,
                    job_id INT NOT NULL,
                    application_id INT NULL,
                    original_name VARCHAR(255) NOT NULL,
                    file_name VARCHAR(255) NOT NULL,
                    file_size INT NOT NULL,
                    file_type VARCHAR(100) NULL,
                    file_data LONGBLOB NOT NULL,
                    upload_time DATETIME DEFAULT CURRENT_TIMESTAMP,
                    INDEX (genius_id),
                    INDEX (job_id),
                    INDEX (application_id)
                )
            """
            cursor.execute(create_table_query)
            conn.commit()
            print("tracker_files table created successfully")

        cursor.close()
        conn.close()

        return jsonify(success=True, message="Tracker file storage setup completed")

    except Exception as e:
        print(f"Error setting up tracker file storage: {e}")
        return jsonify(success=False, error=str(e))

@app.route('/api/tracker/upload_file', methods=['POST'])
@login_required
def upload_tracker_file():
    """
    Upload a file for tracker
    """
    # Only geniuses can access this endpoint
    if session.get('user_type') != 'genius':
        return jsonify(success=False, error="Access denied")

    try:
        if 'file' not in request.files:
            return jsonify(success=False, error="No file provided")

        file = request.files['file']
        job_id = request.form.get('job_id')
        upload_type = request.form.get('upload_type', 'tracker_files')

        if not file or file.filename == '':
            return jsonify(success=False, error="No file selected")

        if not job_id:
            return jsonify(success=False, error="Job ID is required")

        # Check file size (50MB limit)
        file.seek(0, os.SEEK_END)
        file_size = file.tell()
        file.seek(0)

        MAX_FILE_SIZE = 50 * 1024 * 1024  # 50MB
        if file_size > MAX_FILE_SIZE:
            return jsonify(success=False, error=f"File too large (max 50MB)")

        # Read file data
        file_data = file.read()
        original_name = file.filename
        file_name = secure_filename(original_name)

        # Get MIME type
        mime_type, _ = mimetypes.guess_type(original_name)
        if not mime_type:
            mime_type = 'application/octet-stream'

        conn = get_db_connection()
        cursor = conn.cursor()

        # First ensure the tracker_files table exists
        cursor.execute("SHOW TABLES LIKE 'tracker_files'")
        table_exists = cursor.fetchone() is not None

        if not table_exists:
            # Create the table if it doesn't exist
            create_table_query = """
                CREATE TABLE tracker_files (
                    id INT AUTO_INCREMENT PRIMARY KEY,
                    genius_id INT NOT NULL,
                    job_id INT NOT NULL,
                    application_id INT NULL,
                    original_name VARCHAR(255) NOT NULL,
                    file_name VARCHAR(255) NOT NULL,
                    file_size INT NOT NULL,
                    file_type VARCHAR(100) NULL,
                    file_data LONGBLOB NOT NULL,
                    upload_time DATETIME DEFAULT CURRENT_TIMESTAMP,
                    INDEX (genius_id),
                    INDEX (job_id),
                    INDEX (application_id)
                )
            """
            cursor.execute(create_table_query)
            conn.commit()

        # Insert file into tracker_files table
        insert_query = """
            INSERT INTO tracker_files
            (genius_id, job_id, original_name, file_name, file_size, file_type, file_data)
            VALUES (%s, %s, %s, %s, %s, %s, %s)
        """
        cursor.execute(insert_query, (
            session.get('user_id'),
            job_id,
            original_name,
            file_name,
            file_size,
            mime_type,
            file_data
        ))

        file_id = cursor.lastrowid
        conn.commit()

        cursor.close()
        conn.close()

        return jsonify(
            success=True,
            data={
                'file_id': file_id,
                'file_path': f'/api/tracker/download_file/{file_id}',
                'upload_time': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
                'original_name': original_name,
                'file_size': file_size,
                'file_type': mime_type
            }
        )

    except Exception as e:
        print(f"Error uploading tracker file: {e}")
        return jsonify(success=False, error=str(e))

@app.route('/api/tracker/get_files/<int:job_id>')
@login_required
def get_tracker_files(job_id):
    """
    Get all uploaded files for a specific job
    """
    # Only geniuses can access this endpoint
    if session.get('user_type') != 'genius':
        return jsonify(success=False, error="Access denied")

    try:
        conn = get_db_connection()
        cursor = conn.cursor(dictionary=True)

        # Check if tracker_files table exists
        cursor.execute("SHOW TABLES LIKE 'tracker_files'")
        table_exists = cursor.fetchone() is not None

        if not table_exists:
            return jsonify(success=True, files=[])

        # Get files for this job and genius
        query = """
            SELECT id, original_name, file_name, file_size, file_type, upload_time
            FROM tracker_files
            WHERE job_id = %s AND genius_id = %s
            ORDER BY upload_time DESC
        """
        cursor.execute(query, (job_id, session.get('user_id')))
        files = cursor.fetchall()

        # Format the files data
        formatted_files = []
        for file_data in files:
            formatted_files.append({
                'file_id': file_data['id'],
                'original_name': file_data['original_name'],
                'file_name': file_data['file_name'],
                'file_size': file_data['file_size'],
                'file_type': file_data['file_type'],
                'upload_time': file_data['upload_time'].strftime('%Y-%m-%d %H:%M:%S') if file_data['upload_time'] else None
            })

        cursor.close()
        conn.close()

        return jsonify(success=True, files=formatted_files)

    except Exception as e:
        print(f"Error getting tracker files: {e}")
        return jsonify(success=False, error=str(e))

@app.route('/api/tracker/download_file/<int:file_id>')
@login_required
def download_tracker_file(file_id):
    """
    Download a tracker file
    """
    # Only geniuses can access this endpoint
    if session.get('user_type') != 'genius':
        return jsonify(success=False, error="Access denied"), 403

    try:
        conn = get_db_connection()
        cursor = conn.cursor(dictionary=True)

        # Get the file data
        query = """
            SELECT original_name, file_name, file_size, file_type, file_data
            FROM tracker_files
            WHERE id = %s AND genius_id = %s
        """
        cursor.execute(query, (file_id, session.get('user_id')))
        file_data = cursor.fetchone()

        cursor.close()
        conn.close()

        if not file_data:
            return jsonify(success=False, error="File not found"), 404

        # Create a file-like object from the binary data
        file_obj = io.BytesIO(file_data['file_data'])

        return send_file(
            file_obj,
            as_attachment=True,
            download_name=file_data['original_name'],
            mimetype=file_data['file_type'] or 'application/octet-stream'
        )

    except Exception as e:
        print(f"Error downloading tracker file: {e}")
        return jsonify(success=False, error=str(e)), 500

@app.route('/api/tracker/delete_file/<int:file_id>', methods=['DELETE'])
@login_required
def delete_tracker_file(file_id):
    """
    Delete a tracker file
    """
    # Only geniuses can access this endpoint
    if session.get('user_type') != 'genius':
        return jsonify(success=False, error="Access denied")

    try:
        conn = get_db_connection()
        cursor = conn.cursor()

        # Delete the file (only if it belongs to the current genius)
        query = """
            DELETE FROM tracker_files
            WHERE id = %s AND genius_id = %s
        """
        cursor.execute(query, (file_id, session.get('user_id')))

        if cursor.rowcount == 0:
            cursor.close()
            conn.close()
            return jsonify(success=False, error="File not found or access denied")

        conn.commit()
        cursor.close()
        conn.close()

        return jsonify(success=True, message="File deleted successfully")

    except Exception as e:
        print(f"Error deleting tracker file: {e}")
        return jsonify(success=False, error=str(e))

@app.route('/api/tracker/save_time_log', methods=['POST'])
@login_required
def save_time_log():
    """
    Save a new time log entry to approve_genius table
    """
    # Only geniuses can access this endpoint
    if session.get('user_type') != 'genius':
        return jsonify(success=False, error="Access denied")

    try:
        data = request.get_json()
        job_id = data.get('job_id')
        application_id = data.get('application_id')
        date = data.get('date')
        time_in = data.get('time_in')
        time_out = data.get('time_out')
        duration = data.get('duration')
        is_manual = data.get('is_manual', False)
        notes = data.get('notes', '')

        if not all([job_id, date, duration]):
            return jsonify(success=False, error="Missing required fields")

        # Determine time of day based on time_in
        time_of_day = 'Morning'  # Default
        if time_in:
            try:
                from datetime import datetime
                time_obj = datetime.strptime(time_in, '%H:%M:%S').time()
                hour = time_obj.hour

                if 5 <= hour < 12:
                    time_of_day = 'Morning'
                elif 12 <= hour < 17:
                    time_of_day = 'Afternoon'
                else:
                    time_of_day = 'Evening'
            except:
                time_of_day = 'Morning'  # Default if parsing fails

        # Determine session type
        session_type = 'Manual' if is_manual else 'Timer'

        # Calculate duration in hours for duration_hours field
        duration_hours = 0.0
        if duration:
            try:
                # Handle different duration formats
                if ':' in str(duration):
                    # Format like "1:30:00" or "1:30"
                    parts = str(duration).split(':')
                    if len(parts) >= 2:
                        hours = float(parts[0])
                        minutes = float(parts[1])
                        duration_hours = hours + (minutes / 60.0)
                        if len(parts) >= 3:
                            seconds = float(parts[2])
                            duration_hours += (seconds / 3600.0)
                else:
                    # Try to parse as decimal hours
                    duration_hours = float(duration)
            except (ValueError, TypeError):
                duration_hours = 0.0

        conn = get_db_connection()
        cursor = conn.cursor()

        # Update the approve_genius table with the latest time tracking session
        query = """
            UPDATE approve_genius
            SET
                date = %s,
                time_in = %s,
                time_out = %s,
                duration = %s,
                duration_hours = %s,
                type = %s,
                time_of_day = %s,
                job_id = %s,
                application_id = %s,
                session_notes = %s,
                last_session_date = NOW()
            WHERE id = %s
        """

        # Debug: Print the values being saved
        print(f"Saving time log data:")
        print(f"  date: {date}")
        print(f"  time_in: {time_in}")
        print(f"  time_out: {time_out}")
        print(f"  duration: {duration}")
        print(f"  type: {session_type}")
        print(f"  time_of_day: {time_of_day}")
        print(f"  job_id: {job_id}")
        print(f"  application_id: {application_id}")
        print(f"  user_id: {session.get('user_id')}")

        cursor.execute(query, (
            date,
            time_in,
            time_out,
            duration,
            duration_hours,
            session_type,
            time_of_day,
            job_id,
            application_id,
            notes,
            session.get('user_id')
        ))
        conn.commit()
        print("Time log saved successfully to approve_genius table")

        # Also insert into time_logs table for historical tracking
        cursor.execute("SHOW TABLES LIKE 'time_logs'")
        table_exists = cursor.fetchone() is not None

        if table_exists:
            # Insert into time_logs for historical data - ALWAYS INSERT NEW RECORD
            print(f"Inserting new time log entry into time_logs table...")
            insert_query = """
                INSERT INTO time_logs (
                    genius_id, job_id, application_id, date, time_in, time_out,
                    duration, duration_hours, is_manual, notes, created_at, updated_at
                ) VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, NOW(), NOW())
            """

            # Convert time strings to proper TIME format for database
            time_in_db = None
            time_out_db = None
            duration_db = None

            if time_in:
                # Ensure time_in is in HH:MM:SS format
                if len(str(time_in).split(':')) == 2:
                    time_in_db = f"{time_in}:00"
                else:
                    time_in_db = str(time_in)

            if time_out:
                # Ensure time_out is in HH:MM:SS format
                if len(str(time_out).split(':')) == 2:
                    time_out_db = f"{time_out}:00"
                else:
                    time_out_db = str(time_out)

            if duration:
                # Convert duration to TIME format (HH:MM:SS)
                if ':' in str(duration):
                    duration_parts = str(duration).split(':')
                    if len(duration_parts) == 2:
                        duration_db = f"{duration}:00"
                    else:
                        duration_db = str(duration)
                else:
                    # If duration is in decimal hours, convert to HH:MM:SS
                    try:
                        hours = float(duration)
                        hours_int = int(hours)
                        minutes = int((hours - hours_int) * 60)
                        duration_db = f"{hours_int:02d}:{minutes:02d}:00"
                    except:
                        duration_db = "00:00:00"

            print(f"Time logs data: genius_id={session.get('user_id')}, job_id={job_id}, date={date}")
            print(f"Time data: time_in={time_in_db}, time_out={time_out_db}, duration={duration_db}")

            cursor.execute(insert_query, (
                session.get('user_id'),
                job_id,
                application_id,
                date,
                time_in_db,
                time_out_db,
                duration_db,
                duration_hours,
                is_manual,
                notes
            ))
            conn.commit()
            print(f"✅ New time log entry saved to time_logs table successfully!")
        else:
            print("⚠️ time_logs table does not exist, creating it now...")
            # Create the time_logs table if it doesn't exist
            create_table_query = """
                CREATE TABLE time_logs (
                    id INT AUTO_INCREMENT PRIMARY KEY,
                    genius_id INT NOT NULL,
                    job_id INT NOT NULL,
                    application_id INT NOT NULL,
                    date DATE NOT NULL,
                    time_in TIME NULL,
                    time_out TIME NULL,
                    duration TIME NOT NULL,
                    duration_hours DECIMAL(5,2) DEFAULT 0.00,
                    is_manual BOOLEAN DEFAULT FALSE,
                    notes TEXT NULL,
                    proof_filename VARCHAR(255) NULL,
                    proof_data LONGBLOB NULL,
                    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                    INDEX (genius_id),
                    INDEX (job_id),
                    INDEX (application_id),
                    INDEX (date)
                )
            """
            cursor.execute(create_table_query)
            conn.commit()
            print("✅ time_logs table created successfully!")

            # Now insert the current record
            print(f"Inserting first time log entry...")
            insert_query = """
                INSERT INTO time_logs (
                    genius_id, job_id, application_id, date, time_in, time_out,
                    duration, duration_hours, is_manual, notes, created_at, updated_at
                ) VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, NOW(), NOW())
            """
            cursor.execute(insert_query, (
                session.get('user_id'),
                job_id,
                application_id,
                date,
                time_in_db,
                time_out_db,
                duration_db,
                duration_hours,
                is_manual,
                notes
            ))
            conn.commit()
            print(f"✅ First time log entry saved successfully!")

        cursor.close()
        conn.close()

        return jsonify(success=True, message="Time log saved successfully to approve_genius table")

    except Exception as e:
        print(f"Error saving time log: {e}")
        return jsonify(success=False, error=str(e))

@app.route('/my_proposal')
@login_required
def my_proposal():
    """
    Display proposals page for genius users
    """
    # Only geniuses can access this page
    if session.get('user_type') != 'genius':
        return redirect(url_for('landing_page'))

    try:
        conn = get_db_connection()
        cursor = conn.cursor(dictionary=True)

        # Get genius applications/proposals statistics
        genius_id = session.get('user_id')

        # Check if applications table exists
        check_table_query = """
            SELECT COUNT(*)
            FROM information_schema.tables
            WHERE table_schema = 'giggenius'
            AND table_name = 'applications'
        """
        cursor.execute(check_table_query)
        table_exists = cursor.fetchone()['COUNT(*)'] > 0

        # Initialize default stats
        stats = {
            'total_proposals': 0,
            'active_offers': 0,
            'success_rate': 0,
            'potential_earnings': 0
        }

        if table_exists:
            # Get total proposals count
            total_query = """
                SELECT COUNT(*) as total
                FROM applications
                WHERE genius_id = %s
            """
            cursor.execute(total_query, (genius_id,))
            result = cursor.fetchone()
            stats['total_proposals'] = result['total'] if result else 0

            # Get active offers (accepted applications)
            active_query = """
                SELECT COUNT(*) as active
                FROM applications
                WHERE genius_id = %s AND status = 'accepted'
            """
            cursor.execute(active_query, (genius_id,))
            result = cursor.fetchone()
            stats['active_offers'] = result['active'] if result else 0

            # Calculate success rate
            if stats['total_proposals'] > 0:
                stats['success_rate'] = round((stats['active_offers'] / stats['total_proposals']) * 100, 1)

            # Calculate potential earnings (placeholder calculation)
            stats['potential_earnings'] = stats['active_offers'] * 500  # Assuming $500 per active offer

        cursor.close()
        conn.close()

        # Render the My_proposal.html template with stats
        return render_template('My_proposal.html', stats=stats)

    except Exception as e:
        print(f"Error fetching proposal stats: {e}")
        # Provide default stats in case of error
        default_stats = {
            'total_proposals': 0,
            'active_offers': 0,
            'success_rate': 0,
            'potential_earnings': 0
        }
        return render_template('My_proposal.html', stats=default_stats)

@app.route('/applications')
@login_required
def applications():
    """
    Display job applications:
    - For clients: Show applications for their jobs
    - For geniuses: Show their submitted applications
    """
    try:
        conn = get_db_connection()
        cursor = conn.cursor(dictionary=True)

        # Check if applications table exists
        check_table_query = """
            SELECT COUNT(*)
            FROM information_schema.tables
            WHERE table_schema = 'giggenius'
            AND table_name = 'applications'
        """
        cursor.execute(check_table_query)
        table_exists = cursor.fetchone()['COUNT(*)'] > 0

        applications = []

        if table_exists:
            if session.get('user_type') == 'client':
                # For clients, show applications for their jobs
                query = """
                    SELECT
                        a.id, a.genius_id, js.client_id, a.job_id,
                        ag.first_name, ag.last_name, ag.profile_photo,
                        a.position, a.status, a.created_at, a.updated_at,
                        js.title as job_title, js.description as job_description
                    FROM applications a
                    JOIN job_submissions js ON a.job_id = js.id
                    JOIN approve_genius ag ON a.genius_id = ag.id
                    WHERE js.client_id = %s
                    ORDER BY a.created_at DESC
                """
                cursor.execute(query, (session.get('user_id'),))
                applications = cursor.fetchall()

            elif session.get('user_type') == 'genius':
                # For geniuses, show their submitted applications
                query = """
                    SELECT
                        a.id, a.genius_id, js.client_id, a.job_id,
                        ag.first_name, ag.last_name, ag.profile_photo,
                        a.position, a.status, a.created_at, a.updated_at,
                        js.title as job_title, js.description as job_description,
                        ac.first_name as client_first_name, ac.last_name as client_last_name,
                        ac.business_name
                    FROM applications a
                    JOIN job_submissions js ON a.job_id = js.id
                    JOIN approve_genius ag ON a.genius_id = ag.id
                    JOIN approve_client ac ON js.client_id = ac.id
                    WHERE a.genius_id = %s
                    ORDER BY a.created_at DESC
                """
                cursor.execute(query, (session.get('user_id'),))
                applications = cursor.fetchall()

        # Format dates and process application data
        for app in applications:
            if 'created_at' in app and app['created_at']:
                app['created_at'] = app['created_at'].strftime('%B %d, %Y')
            if 'updated_at' in app and app['updated_at']:
                app['updated_at'] = app['updated_at'].strftime('%B %d, %Y')

            # Truncate description
            if 'job_description' in app and app['job_description']:
                app['job_description_short'] = app['job_description'][:150] + '...' if len(app['job_description']) > 150 else app['job_description']

            # Set default status if not present
            if 'status' not in app or not app['status']:
                app['status'] = 'pending'

        cursor.close()
        conn.close()

        return render_template('applications.html', applications=applications)

    except Exception as e:
        print(f"Error fetching applications: {e}")
        return render_template('applications.html', applications=[], error=str(e))

@app.route('/delete_application', methods=['POST'])
@login_required
def delete_application():
    """Delete a job application"""
    data = request.get_json()
    application_id = data.get('application_id')
    job_id = data.get('job_id')

    if not application_id and not job_id:
        return jsonify(success=False, error="Application ID or Job ID is required")

    try:
        conn = get_db_connection()
        cursor = conn.cursor(dictionary=True)

        # Determine the query based on provided parameters
        if application_id:
            # First verify that this user owns the application
            verify_query = """
                SELECT a.id, a.genius_id, js.client_id, a.job_id
                FROM applications a
                JOIN job_submissions js ON a.job_id = js.id
                WHERE a.id = %s
            """
            cursor.execute(verify_query, (application_id,))
            application = cursor.fetchone()

            if not application:
                cursor.close()
                conn.close()
                return jsonify(success=False, error="Application not found")

            # Check if user has permission to delete
            if session.get('user_type') == 'genius' and application['genius_id'] != session.get('user_id'):
                cursor.close()
                conn.close()
                return jsonify(success=False, error="You don't have permission to delete this application")

            if session.get('user_type') == 'client' and application['client_id'] != session.get('user_id'):
                cursor.close()
                conn.close()
                return jsonify(success=False, error="You don't have permission to delete this application")

            # Delete the application
            delete_query = "DELETE FROM applications WHERE id = %s"
            cursor.execute(delete_query, (application_id,))

            # Also delete any related messages
            delete_messages_query = "DELETE FROM messages WHERE related_to_application_id = %s"
            try:
                cursor.execute(delete_messages_query, (application_id,))
            except Exception as e:
                print(f"Error deleting related messages: {e}")
                # Continue even if message deletion fails

        elif job_id and session.get('user_type') == 'genius':
            # Delete application by genius_id and job_id
            delete_query = """
                DELETE FROM applications
                WHERE genius_id = %s AND job_id = %s
            """
            cursor.execute(delete_query, (session.get('user_id'), job_id))

            # Get the deleted application ID for message deletion
            # This won't work after deletion, so we need to do it before
            # But we've already executed the delete query, so we'll skip message deletion in this case

        conn.commit()
        rows_affected = cursor.rowcount

        cursor.close()
        conn.close()

        if rows_affected > 0:
            return jsonify(success=True, message="Application deleted successfully")
        else:
            return jsonify(success=False, error="No application was found to delete")

    except Exception as e:
        print(f"Error deleting application: {e}")
        return jsonify(success=False, error="An error occurred while deleting the application")

@app.route('/update_application_status', methods=['POST'])
@login_required
def update_application_status():
    """Update the status of a job application"""
    if session.get('user_type') != 'client':
        return jsonify(success=False, error="Only clients can update application status")

    data = request.get_json()
    application_id = data.get('application_id')
    new_status = data.get('status')

    if not application_id or not new_status:
        return jsonify(success=False, error="Application ID and status are required")

    try:
        conn = get_db_connection()
        cursor = conn.cursor(dictionary=True)

        # First verify that this client owns the job for this application
        verify_query = """
            SELECT a.id, a.genius_id, js.client_id, a.job_id, js.title as job_title,
                   a.first_name, a.last_name
            FROM applications a
            JOIN job_submissions js ON a.job_id = js.id
            WHERE a.id = %s
        """
        cursor.execute(verify_query, (application_id,))
        application = cursor.fetchone()

        if not application or application['client_id'] != session.get('user_id'):
            cursor.close()
            conn.close()
            return jsonify(success=False, error="You don't have permission to update this application")

        # Update the application status
        update_query = """
            UPDATE applications
            SET status = %s, updated_at = NOW()
            WHERE id = %s
        """
        cursor.execute(update_query, (new_status, application_id))
        conn.commit()

        # If the status is 'accepted', send an automatic message to the genius
        if new_status == 'accepted':
            try:
                # Check if messages table exists and has the correct structure
                check_table_query = """
                    SELECT COUNT(*)
                    FROM information_schema.tables
                    WHERE table_schema = 'giggenius'
                    AND table_name = 'messages'
                """
                cursor.execute(check_table_query)
                table_exists = cursor.fetchone()['COUNT(*)'] > 0

                if not table_exists:
                    # Create messages table with the correct structure
                    create_table_query = """
                        CREATE TABLE messages (
                            id INT AUTO_INCREMENT PRIMARY KEY,
                            sender_id INT NOT NULL,
                            sender_type VARCHAR(50) DEFAULT 'user',
                            receiver_id INT NOT NULL,
                            receiver_type VARCHAR(50) DEFAULT 'user',
                            message_text TEXT NOT NULL,
                            is_read BOOLEAN DEFAULT FALSE,
                            is_auto BOOLEAN DEFAULT FALSE,
                            related_to_job_id INT NULL,
                            related_to_application_id INT NULL,
                            message_type VARCHAR(50) DEFAULT 'text',
                            timestamp DATETIME NOT NULL,
                            status VARCHAR(50) DEFAULT 'sent',
                            is_deleted BOOLEAN DEFAULT FALSE,
                            deleted_by_sender BOOLEAN DEFAULT FALSE,
                            deleted_by_receiver BOOLEAN DEFAULT FALSE,
                            reply_to_id INT NULL,
                            replied_message_text TEXT NULL,
                            file_data LONGBLOB NULL,
                            file_name VARCHAR(255) NULL,
                            file_mime_type VARCHAR(100) NULL,
                            file_url VARCHAR(255) NULL,
                            INDEX (sender_id),
                            INDEX (receiver_id),
                            INDEX (related_to_job_id),
                            INDEX (related_to_application_id),
                            INDEX (reply_to_id)
                        )
                    """
                    cursor.execute(create_table_query)
                    conn.commit()
                    print("Successfully created messages table")
                else:
                    # Check if the table has the correct structure
                    check_columns_query = """
                        SELECT COLUMN_NAME
                        FROM INFORMATION_SCHEMA.COLUMNS
                        WHERE TABLE_SCHEMA = 'giggenius'
                        AND TABLE_NAME = 'messages'
                    """
                    cursor.execute(check_columns_query)
                    columns = [row['COLUMN_NAME'] for row in cursor.fetchall()]

                    # Check for required columns and add them if missing
                    required_columns = {
                        'sender_type': "VARCHAR(50) DEFAULT 'user' AFTER sender_id",
                        'receiver_type': "VARCHAR(50) DEFAULT 'user' AFTER receiver_id",
                        'message_text': "TEXT NOT NULL AFTER receiver_type",
                        'is_read': "BOOLEAN DEFAULT FALSE AFTER message_text",
                        'is_auto': "BOOLEAN DEFAULT FALSE AFTER is_read",
                        'related_to_job_id': "INT NULL AFTER is_auto",
                        'related_to_application_id': "INT NULL AFTER related_to_job_id",
                        'message_type': "VARCHAR(50) DEFAULT 'text' AFTER related_to_application_id",
                        'timestamp': "DATETIME NOT NULL AFTER message_type",
                        'status': "VARCHAR(50) DEFAULT 'sent' AFTER timestamp",
                        'is_deleted': "BOOLEAN DEFAULT FALSE AFTER status",
                        'deleted_by_sender': "BOOLEAN DEFAULT FALSE AFTER is_deleted",
                        'deleted_by_receiver': "BOOLEAN DEFAULT FALSE AFTER deleted_by_sender",
                        'reply_to_id': "INT NULL AFTER deleted_by_receiver",
                        'replied_message_text': "TEXT NULL AFTER reply_to_id",
                        'file_data': "LONGBLOB NULL AFTER replied_message_text",
                        'file_name': "VARCHAR(255) NULL AFTER file_data",
                        'file_mime_type': "VARCHAR(100) NULL AFTER file_name",
                        'file_url': "VARCHAR(255) NULL AFTER file_mime_type"
                    }

                    # Special case for message vs message_text
                    if 'message_text' not in columns and 'message' in columns:
                        # Rename message column to message_text
                        alter_query = """
                            ALTER TABLE messages
                            CHANGE COLUMN message message_text TEXT NOT NULL
                        """
                        cursor.execute(alter_query)
                        conn.commit()
                        print("Renamed 'message' column to 'message_text'")
                        columns.append('message_text')

                    # Add all missing columns
                    for column, definition in required_columns.items():
                        if column not in columns:
                            alter_query = f"""
                                ALTER TABLE messages
                                ADD COLUMN {column} {definition}
                            """
                            cursor.execute(alter_query)
                            conn.commit()
                            print(f"Added '{column}' column to messages table")
            except Exception as e:
                print(f"Error checking/updating messages table structure: {e}")
                # Continue with the process, we'll handle errors later

            # Send automatic acceptance message
            genius_id = application['genius_id']
            client_id = application['client_id']
            job_id = application['job_id']
            job_title = application['job_title']

            # Get client name
            client_query = """
                SELECT first_name, last_name, business_name
                FROM approve_client
                WHERE id = %s
            """
            cursor.execute(client_query, (client_id,))
            client_info = cursor.fetchone()

            # Get client name for personalized message
            client_name = "Client"
            if client_info:
                if client_info['business_name']:
                    client_name = client_info['business_name']
                elif client_info['first_name'] and client_info['last_name']:
                    client_name = f"{client_info['first_name']} {client_info['last_name']}"

            # Personalized acceptance message
            genius_name = f"{application['first_name']}"
            acceptance_message = f"Congratulations {genius_name}! Your application for '{job_title}' has been accepted by {client_name}. I look forward to working with you! 👋"

            # Insert message into database
            insert_message_query = """
                INSERT INTO messages (
                    sender_id, sender_type, receiver_id, receiver_type, message_text,
                    is_read, is_auto, related_to_job_id, related_to_application_id,
                    message_type, timestamp, status, is_deleted, deleted_by_sender,
                    deleted_by_receiver
                ) VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, NOW(), %s, %s, %s, %s)
            """
            cursor.execute(
                insert_message_query,
                (
                    client_id,
                    'client',  # sender_type
                    genius_id,
                    'genius',  # receiver_type
                    acceptance_message,
                    False,     # is_read
                    True,      # is_auto
                    job_id,
                    application_id,
                    'text',    # message_type
                    'sent',    # status
                    False,     # is_deleted
                    False,     # deleted_by_sender
                    False      # deleted_by_receiver
                )
            )
            conn.commit()

            print(f"Automatic acceptance message sent from client {client_id} to genius {genius_id}")

        cursor.close()
        conn.close()

        return jsonify(
            success=True,
            message=f"Application status updated to {new_status}",
            automatic_message_sent=(new_status == 'accepted')
        )

    except Exception as e:
        print(f"Error updating application status: {e}")
        return jsonify(success=False, error="An error occurred while updating the application status")



@app.route('/api/applications')
@login_required
def api_applications():
    """API endpoint to get applications for the chat feature"""
    try:
        conn = get_db_connection()
        cursor = conn.cursor(dictionary=True)

        # Check if applications table exists
        check_table_query = """
            SELECT COUNT(*)
            FROM information_schema.tables
            WHERE table_schema = 'giggenius'
            AND table_name = 'applications'
        """
        cursor.execute(check_table_query)
        table_exists = cursor.fetchone()['COUNT(*)'] > 0

        applications = []

        if table_exists:
            if session.get('user_type') == 'client':
                # For clients, get applications for their jobs
                query = """
                    SELECT
                        a.id, a.genius_id, js.client_id, a.job_id,
                        a.first_name, a.last_name, a.profile_photo,
                        a.position, a.status, a.created_at, a.updated_at,
                        js.title as job_title
                    FROM applications a
                    JOIN job_submissions js ON a.job_id = js.id
                    WHERE js.client_id = %s
                    ORDER BY a.created_at DESC
                """
                cursor.execute(query, (session.get('user_id'),))
                applications = cursor.fetchall()

            elif session.get('user_type') == 'genius':
                # For geniuses, get their submitted applications
                query = """
                    SELECT
                        a.id, a.genius_id, js.client_id, a.job_id,
                        a.first_name, a.last_name, a.profile_photo,
                        a.position, a.status, a.created_at, a.updated_at,
                        js.title as job_title,
                        ac.first_name as client_first_name, ac.last_name as client_last_name,
                        ac.business_name as client_name
                    FROM applications a
                    JOIN job_submissions js ON a.job_id = js.id
                    JOIN approve_client ac ON js.client_id = ac.id
                    WHERE a.genius_id = %s
                    ORDER BY a.created_at DESC
                """
                cursor.execute(query, (session.get('user_id'),))
                applications = cursor.fetchall()

        # Format dates for JSON serialization
        for app in applications:
            # Create copies of fields to modify to avoid changing dict size during iteration
            datetime_fields = {}

            # First identify all datetime fields
            for key, value in app.items():
                if isinstance(value, datetime):
                    datetime_fields[key] = value

            # Then format datetime fields
            for key, value in datetime_fields.items():
                app[key] = value.strftime('%Y-%m-%dT%H:%M:%S')

        cursor.close()
        conn.close()

        return jsonify(success=True, applications=applications)

    except Exception as e:
        print(f"Error fetching applications for API: {e}")
        return jsonify(success=False, error=str(e), applications=[])

@app.route('/api/messages')
@login_required
def api_messages():
    """API endpoint to get messages for the current user"""
    try:
        conn = get_db_connection()
        cursor = conn.cursor(dictionary=True)

        # Check if messages table exists
        check_table_query = """
            SELECT COUNT(*)
            FROM information_schema.tables
            WHERE table_schema = 'giggenius'
            AND table_name = 'messages'
        """
        cursor.execute(check_table_query)
        table_exists = cursor.fetchone()['COUNT(*)'] > 0

        messages = []

        if table_exists:
            # Get all messages where the current user is either sender or receiver
            query = """
                SELECT
                    m.id, m.sender_id, m.sender_type, m.receiver_id, m.receiver_type,
                    m.message_text as message, m.is_read, m.is_auto,
                    m.related_to_job_id, m.related_to_application_id,
                    m.message_type, m.timestamp, m.status, m.is_deleted,
                    m.deleted_by_sender, m.deleted_by_receiver,
                    m.reply_to_id, m.replied_message_text,
                    m.file_name, m.file_mime_type, m.file_url
                FROM messages m
                WHERE m.sender_id = %s OR m.receiver_id = %s
                ORDER BY m.timestamp ASC
            """
            cursor.execute(query, (session.get('user_id'), session.get('user_id')))
            messages = cursor.fetchall()

            # Format dates and handle binary data for JSON serialization
            for msg in messages:
                # Create copies of fields to modify to avoid changing dict size during iteration
                datetime_fields = {}
                binary_fields = {}

                # First identify all datetime and binary fields
                for key, value in msg.items():
                    if isinstance(value, datetime):
                        datetime_fields[key] = value
                    elif isinstance(value, bytes):
                        binary_fields[key] = value

                # Then format datetime fields
                for key, value in datetime_fields.items():
                    msg[key] = value.strftime('%Y-%m-%dT%H:%M:%S')

                # Handle binary data
                for key, value in binary_fields.items():
                    if key == 'file_data':
                        msg[key] = None  # Set to None if binary data

        cursor.close()
        conn.close()

        return jsonify(success=True, messages=messages)

    except Exception as e:
        print(f"Error fetching messages for API: {e}")
        return jsonify(success=False, error=str(e), messages=[])

@app.route('/api/messages/batch')
@login_required
def api_batch_messages():
    """API endpoint to get multiple messages by IDs in a single request"""
    try:
        # Get message IDs from query parameter
        message_ids_str = request.args.get('ids', '')
        if not message_ids_str:
            return jsonify(success=False, error="No message IDs provided")

        try:
            # Parse message IDs
            message_ids = [int(id_str) for id_str in message_ids_str.split(',') if id_str.strip()]

            # Limit to 50 messages at a time to prevent abuse
            message_ids = message_ids[:50]

            if not message_ids:
                return jsonify(success=False, error="Invalid message IDs")
        except ValueError:
            return jsonify(success=False, error="Invalid message ID format")

        conn = get_db_connection()
        cursor = conn.cursor(dictionary=True)

        # Get messages by IDs
        placeholders = ', '.join(['%s'] * len(message_ids))
        query = f"""
            SELECT m.*,
                   CASE
                       WHEN m.sender_type = 'genius' THEN g.first_name
                       ELSE c.first_name
                   END AS sender_first_name,
                   CASE
                       WHEN m.sender_type = 'genius' THEN g.last_name
                       ELSE c.last_name
                   END AS sender_last_name,
                   CASE
                       WHEN m.receiver_type = 'genius' THEN g2.first_name
                       ELSE c2.first_name
                   END AS receiver_first_name,
                   CASE
                       WHEN m.receiver_type = 'genius' THEN g2.last_name
                       ELSE c2.last_name
                   END AS receiver_last_name
            FROM messages m
            LEFT JOIN approve_genius g ON m.sender_id = g.id AND m.sender_type = 'genius'
            LEFT JOIN approve_client c ON m.sender_id = c.id AND m.sender_type = 'client'
            LEFT JOIN approve_genius g2 ON m.receiver_id = g2.id AND m.receiver_type = 'genius'
            LEFT JOIN approve_client c2 ON m.receiver_id = c2.id AND m.receiver_type = 'client'
            WHERE m.id IN ({placeholders})
            ORDER BY m.timestamp DESC
        """
        cursor.execute(query, message_ids)
        messages = cursor.fetchall()

        # Format messages for JSON response
        formatted_messages = []
        for msg in messages:
            formatted_msg = dict(msg)
            # Convert datetime objects to strings
            for key, value in formatted_msg.items():
                if isinstance(value, datetime):
                    formatted_msg[key] = value.strftime('%Y-%m-%dT%H:%M:%S.000Z')
            formatted_messages.append(formatted_msg)

        cursor.close()
        conn.close()

        return jsonify(success=True, messages=formatted_messages)

    except Exception as e:
        print(f"Error fetching batch messages: {e}")
        return jsonify(success=False, error=str(e), messages=[])

@app.route('/api/messages/<int:contact_id>')
@login_required
def api_messages_with_contact(contact_id):
    """API endpoint to get messages between current user and a specific contact"""
    try:
        # Get pagination parameters
        page = request.args.get('page', 1, type=int)
        limit = request.args.get('limit', 20, type=int)  # Default to 20 messages per page
        before_id = request.args.get('before_id', None, type=int)  # For loading older messages

        # Limit the maximum number of messages per request
        if limit > 50:
            limit = 50

        conn = get_db_connection()
        cursor = conn.cursor(dictionary=True)

        # Check if messages table exists
        check_table_query = """
            SELECT COUNT(*) as count
            FROM information_schema.tables
            WHERE table_schema = 'giggenius'
            AND table_name = 'messages'
        """
        cursor.execute(check_table_query)
        table_exists = cursor.fetchone()['count'] > 0

        print(f"Messages table exists: {table_exists}")

        messages = []
        total_count = 0
        has_more = False

        if table_exists:
            # Check if there are any messages in the table
            count_messages_query = """
                SELECT COUNT(*) as count
                FROM messages
            """
            cursor.execute(count_messages_query)
            messages_count = cursor.fetchone()['count']
            print(f"Total messages in database: {messages_count}")

            # Check if there are messages between these users
            check_messages_query = """
                SELECT COUNT(*) as count
                FROM messages m
                WHERE (m.sender_id = %s AND m.receiver_id = %s)
                   OR (m.sender_id = %s AND m.receiver_id = %s)
            """
            cursor.execute(check_messages_query, (session.get('user_id'), contact_id, contact_id, session.get('user_id')))
            conversation_count = cursor.fetchone()['count']
            print(f"Messages between users {session.get('user_id')} and {contact_id}: {conversation_count}")
            # First get the total count of messages
            count_query = """
                SELECT COUNT(*) as total
                FROM messages m
                WHERE (m.sender_id = %s AND m.receiver_id = %s)
                   OR (m.sender_id = %s AND m.receiver_id = %s)
            """
            cursor.execute(count_query, (session.get('user_id'), contact_id, contact_id, session.get('user_id')))
            total_count = cursor.fetchone()['total']

            # Build the query based on pagination parameters
            if before_id:
                # Load messages before a specific message ID (for scrolling up)
                query = """
                    SELECT
                        m.id, m.sender_id, m.sender_type, m.receiver_id, m.receiver_type,
                        m.message_text as message, m.is_read, m.is_auto,
                        m.related_to_job_id, m.related_to_application_id,
                        m.message_type, m.timestamp, m.status, m.is_deleted,
                        m.deleted_by_sender, m.deleted_by_receiver,
                        m.reply_to_id, m.replied_message_text,
                        m.file_name, m.file_mime_type, m.file_url
                    FROM messages m
                    WHERE ((m.sender_id = %s AND m.receiver_id = %s)
                       OR (m.sender_id = %s AND m.receiver_id = %s))
                       AND m.id < %s
                    ORDER BY m.timestamp DESC
                    LIMIT %s
                """
                cursor.execute(query, (
                    session.get('user_id'), contact_id,
                    contact_id, session.get('user_id'),
                    before_id, limit
                ))
                messages = cursor.fetchall()
                # Reverse to get chronological order
                messages.reverse()
            else:
                # Load the most recent messages (for initial load)
                offset = (page - 1) * limit
                query = """
                    SELECT
                        m.id, m.sender_id, m.sender_type, m.receiver_id, m.receiver_type,
                        m.message_text as message, m.is_read, m.is_auto,
                        m.related_to_job_id, m.related_to_application_id,
                        m.message_type, m.timestamp, m.status, m.is_deleted,
                        m.deleted_by_sender, m.deleted_by_receiver,
                        m.reply_to_id, m.replied_message_text,
                        m.file_name, m.file_mime_type, m.file_url
                    FROM messages m
                    WHERE (m.sender_id = %s AND m.receiver_id = %s)
                       OR (m.sender_id = %s AND m.receiver_id = %s)
                    ORDER BY m.timestamp DESC
                    LIMIT %s OFFSET %s
                """
                cursor.execute(query, (
                    session.get('user_id'), contact_id,
                    contact_id, session.get('user_id'),
                    limit, offset
                ))
                messages = cursor.fetchall()
                # Reverse to get chronological order
                messages.reverse()

            # Determine if there are more messages to load
            has_more = len(messages) == limit and (page * limit) < total_count

            # Mark messages as read if current user is the receiver
            if messages:
                update_query = """
                    UPDATE messages
                    SET is_read = TRUE
                    WHERE receiver_id = %s AND sender_id = %s AND is_read = FALSE
                """
                cursor.execute(update_query, (session.get('user_id'), contact_id))
                conn.commit()

            # Check if reactions table exists
            check_reactions_table_query = """
                SELECT COUNT(*) as count
                FROM information_schema.tables
                WHERE table_schema = 'giggenius'
                AND table_name = 'reactions'
            """
            cursor.execute(check_reactions_table_query)
            reactions_table_exists = cursor.fetchone()['count'] > 0

            # Get all reactions for these messages in a single query
            message_reactions = {}
            if reactions_table_exists and messages:
                message_ids = [msg['id'] for msg in messages]
                placeholders = ', '.join(['%s'] * len(message_ids))
                reactions_query = f"""
                    SELECT r.*, m.sender_id, m.receiver_id
                    FROM reactions r
                    JOIN messages m ON r.message_id = m.id
                    WHERE r.message_id IN ({placeholders})
                    ORDER BY r.created_at
                """
                cursor.execute(reactions_query, message_ids)
                all_reactions = cursor.fetchall()

                # Group reactions by message_id
                for reaction in all_reactions:
                    message_id = reaction['message_id']
                    if message_id not in message_reactions:
                        message_reactions[message_id] = []

                    # Format the reaction
                    formatted_reaction = dict(reaction)
                    # Convert datetime objects to strings
                    for key, value in formatted_reaction.items():
                        if isinstance(value, datetime):
                            formatted_reaction[key] = value.strftime('%Y-%m-%dT%H:%M:%S.000Z')

                    message_reactions[message_id].append(formatted_reaction)

            # Format dates and handle binary data for JSON serialization
            for msg in messages:
                # Create copies of fields to modify to avoid changing dict size during iteration
                datetime_fields = {}
                binary_fields = {}

                # First identify all datetime and binary fields
                for key, value in msg.items():
                    if isinstance(value, datetime):
                        datetime_fields[key] = value
                    elif isinstance(value, bytes):
                        binary_fields[key] = value

                # Then format datetime fields
                for key, value in datetime_fields.items():
                    msg[key] = value.strftime('%Y-%m-%dT%H:%M:%S.000Z')

                # Handle binary data
                for key, value in binary_fields.items():
                    if key == 'file_data':
                        msg[key] = None  # Set to None if binary data

                # Add reactions to the message
                msg['reactions'] = message_reactions.get(msg['id'], [])

        cursor.close()
        conn.close()

        return jsonify(
            success=True,
            messages=messages,
            total_count=total_count,
            has_more=has_more,
            page=page,
            limit=limit
        )

    except Exception as e:
        print(f"Error fetching messages for API: {e}")
        return jsonify(success=False, error=str(e), messages=[])

@app.route('/api/messages/<int:contact_id>/read', methods=['POST'])
@login_required
def mark_messages_as_read(contact_id):
    """API endpoint to mark messages from a specific contact as read"""
    try:
        conn = get_db_connection()
        cursor = conn.cursor(dictionary=True)

        # Update messages where current user is the receiver and the sender is the contact
        update_query = """
            UPDATE messages
            SET is_read = TRUE
            WHERE receiver_id = %s AND sender_id = %s AND is_read = FALSE
        """
        cursor.execute(update_query, (session.get('user_id'), contact_id))
        conn.commit()

        rows_affected = cursor.rowcount

        cursor.close()
        conn.close()

        return jsonify(success=True, messages_read=rows_affected)

    except Exception as e:
        print(f"Error marking messages as read: {e}")
        return jsonify(success=False, error=str(e))

@app.route('/api/contacts')
@login_required
def api_contacts():
    """API endpoint to get contacts for the current user"""
    try:
        user_id = session.get('user_id')
        user_type = session.get('user_type')

        # Create a cache key based on user ID and type
        cache_key = f"contacts_{user_id}_{user_type}"

        # Check if we have cached contacts for this user
        if hasattr(api_contacts, 'cache') and cache_key in api_contacts.cache:
            cached_data, timestamp = api_contacts.cache[cache_key]
            # Check if cache is still valid (less than 60 seconds old)
            if time.time() - timestamp < 60:
                return jsonify(success=True, contacts=cached_data)

        conn = get_db_connection()
        cursor = conn.cursor(dictionary=True)

        contacts = []

        # Check if tables exist in a single query
        tables_query = """
            SELECT
                SUM(CASE WHEN table_name = 'messages' THEN 1 ELSE 0 END) as messages_exists,
                SUM(CASE WHEN table_name = 'applications' THEN 1 ELSE 0 END) as applications_exists
            FROM information_schema.tables
            WHERE table_schema = 'giggenius'
            AND table_name IN ('messages', 'applications')
        """
        cursor.execute(tables_query)
        tables_result = cursor.fetchone()

        messages_table_exists = tables_result['messages_exists'] > 0
        applications_table_exists = tables_result['applications_exists'] > 0

        # Use optimized queries based on user type
        if user_type == 'genius':
            # For genius users, get all contacts in one query if possible
            if messages_table_exists and applications_table_exists:
                # Combined query for both messages and applications
                query = """
                    SELECT
                        c.id,
                        c.first_name,
                        c.last_name,
                        c.business_name,
                        NULL as profile_photo,
                        'client' as user_type,
                        COALESCE(m_time, a_time) as last_message_time,
                        COALESCE(unread_count, 0) as unread_count
                    FROM (
                        -- Get all unique clients
                        SELECT DISTINCT ac.id, ac.first_name, ac.last_name, ac.business_name
                        FROM (
                            -- Clients from messages
                            SELECT DISTINCT
                                CASE
                                    WHEN m.sender_id = %s THEN m.receiver_id
                                    ELSE m.sender_id
                                END as client_id
                            FROM messages m
                            WHERE m.sender_id = %s OR m.receiver_id = %s

                            UNION

                            -- Clients from applications
                            SELECT DISTINCT js.client_id
                            FROM applications a
                            JOIN job_submissions js ON a.job_id = js.id
                            WHERE a.genius_id = %s
                        ) as client_ids
                        JOIN approve_client ac ON client_ids.client_id = ac.id
                    ) c
                    LEFT JOIN (
                        -- Get last message time and unread count
                        SELECT
                            CASE
                                WHEN m.sender_id = %s THEN m.receiver_id
                                ELSE m.sender_id
                            END as client_id,
                            MAX(m.timestamp) as m_time,
                            SUM(CASE WHEN m.receiver_id = %s AND m.sender_id != %s AND m.is_read = FALSE THEN 1 ELSE 0 END) as unread_count
                        FROM messages m
                        WHERE m.sender_id = %s OR m.receiver_id = %s
                        GROUP BY client_id
                    ) m ON c.id = m.client_id
                    LEFT JOIN (
                        -- Get last application time
                        SELECT
                            js.client_id,
                            MAX(a.created_at) as a_time
                        FROM applications a
                        JOIN job_submissions js ON a.job_id = js.id
                        WHERE a.genius_id = %s
                        GROUP BY js.client_id
                    ) a ON c.id = a.client_id
                    ORDER BY last_message_time DESC
                """
                cursor.execute(query, (
                    user_id, user_id, user_id, user_id,
                    user_id, user_id, user_id, user_id, user_id,
                    user_id
                ))
                contacts = cursor.fetchall()
            else:
                # Fallback to separate queries
                if messages_table_exists:
                    # Get contacts from messages
                    query = """
                        SELECT
                            ac.id, ac.first_name, ac.last_name, ac.business_name,
                            NULL as profile_photo, 'client' as user_type,
                            MAX(m.timestamp) as last_message_time,
                            SUM(CASE WHEN m.receiver_id = %s AND m.sender_id = ac.id AND m.is_read = FALSE THEN 1 ELSE 0 END) as unread_count
                        FROM messages m
                        JOIN approve_client ac ON (m.sender_id = ac.id OR m.receiver_id = ac.id)
                        WHERE (m.sender_id = %s OR m.receiver_id = %s)
                          AND (m.sender_id != m.receiver_id)  -- Exclude self-messages
                        GROUP BY ac.id, ac.first_name, ac.last_name, ac.business_name
                        ORDER BY last_message_time DESC
                    """
                    cursor.execute(query, (user_id, user_id, user_id))
                    contacts = cursor.fetchall()

                if applications_table_exists:
                    # Get contacts from applications
                    query = """
                        SELECT DISTINCT
                            ac.id, ac.first_name, ac.last_name, ac.business_name,
                            NULL as profile_photo, 'client' as user_type,
                            MAX(a.created_at) as last_message_time,
                            0 as unread_count
                        FROM applications a
                        JOIN job_submissions js ON a.job_id = js.id
                        JOIN approve_client ac ON js.client_id = ac.id
                        WHERE a.genius_id = %s
                        GROUP BY ac.id, ac.first_name, ac.last_name, ac.business_name
                        ORDER BY last_message_time DESC
                    """
                    cursor.execute(query, (user_id,))
                    application_contacts = cursor.fetchall()

                    # Merge with existing contacts
                    existing_ids = [c['id'] for c in contacts]
                    for contact in application_contacts:
                        if contact['id'] not in existing_ids:
                            contacts.append(contact)
                            existing_ids.append(contact['id'])

        elif user_type == 'client':
            # For client users, get all contacts in one query if possible
            if messages_table_exists and applications_table_exists:
                # Combined query for both messages and applications
                query = """
                    SELECT
                        g.id,
                        g.first_name,
                        g.last_name,
                        g.position,
                        NULL as profile_photo,
                        'genius' as user_type,
                        COALESCE(m_time, a_time) as last_message_time,
                        COALESCE(unread_count, 0) as unread_count
                    FROM (
                        -- Get all unique geniuses
                        SELECT DISTINCT ag.id, ag.first_name, ag.last_name, ag.position
                        FROM (
                            -- Geniuses from messages
                            SELECT DISTINCT
                                CASE
                                    WHEN m.sender_id = %s THEN m.receiver_id
                                    ELSE m.sender_id
                                END as genius_id
                            FROM messages m
                            WHERE m.sender_id = %s OR m.receiver_id = %s

                            UNION

                            -- Geniuses from applications
                            SELECT DISTINCT a.genius_id
                            FROM applications a
                            JOIN job_submissions js ON a.job_id = js.id
                            WHERE js.client_id = %s
                        ) as genius_ids
                        JOIN approve_genius ag ON genius_ids.genius_id = ag.id
                    ) g
                    LEFT JOIN (
                        -- Get last message time and unread count
                        SELECT
                            CASE
                                WHEN m.sender_id = %s THEN m.receiver_id
                                ELSE m.sender_id
                            END as genius_id,
                            MAX(m.timestamp) as m_time,
                            SUM(CASE WHEN m.receiver_id = %s AND m.sender_id != %s AND m.is_read = FALSE THEN 1 ELSE 0 END) as unread_count
                        FROM messages m
                        WHERE m.sender_id = %s OR m.receiver_id = %s
                        GROUP BY genius_id
                    ) m ON g.id = m.genius_id
                    LEFT JOIN (
                        -- Get last application time
                        SELECT
                            a.genius_id,
                            MAX(a.created_at) as a_time
                        FROM applications a
                        JOIN job_submissions js ON a.job_id = js.id
                        WHERE js.client_id = %s
                        GROUP BY a.genius_id
                    ) a ON g.id = a.genius_id
                    ORDER BY last_message_time DESC
                """
                cursor.execute(query, (
                    user_id, user_id, user_id, user_id,
                    user_id, user_id, user_id, user_id, user_id,
                    user_id
                ))
                contacts = cursor.fetchall()
            else:
                # Fallback to separate queries
                if messages_table_exists:
                    # Get contacts from messages
                    query = """
                        SELECT
                            ag.id, ag.first_name, ag.last_name, ag.position,
                            NULL as profile_photo, 'genius' as user_type,
                            MAX(m.timestamp) as last_message_time,
                            SUM(CASE WHEN m.receiver_id = %s AND m.sender_id = ag.id AND m.is_read = FALSE THEN 1 ELSE 0 END) as unread_count
                        FROM messages m
                        JOIN approve_genius ag ON (m.sender_id = ag.id OR m.receiver_id = ag.id)
                        WHERE (m.sender_id = %s OR m.receiver_id = %s)
                          AND (m.sender_id != m.receiver_id)  -- Exclude self-messages
                        GROUP BY ag.id, ag.first_name, ag.last_name, ag.position
                        ORDER BY last_message_time DESC
                    """
                    cursor.execute(query, (user_id, user_id, user_id))
                    contacts = cursor.fetchall()

                if applications_table_exists:
                    # Get contacts from applications
                    query = """
                        SELECT DISTINCT
                            ag.id, ag.first_name, ag.last_name, ag.position,
                            NULL as profile_photo, 'genius' as user_type,
                            MAX(a.created_at) as last_message_time,
                            0 as unread_count
                        FROM applications a
                        JOIN job_submissions js ON a.job_id = js.id
                        JOIN approve_genius ag ON a.genius_id = ag.id
                        WHERE js.client_id = %s
                        GROUP BY ag.id, ag.first_name, ag.last_name, ag.position
                        ORDER BY last_message_time DESC
                    """
                    cursor.execute(query, (user_id,))
                    application_contacts = cursor.fetchall()

                    # Merge with existing contacts
                    existing_ids = [c['id'] for c in contacts]
                    for contact in application_contacts:
                        if contact['id'] not in existing_ids:
                            contacts.append(contact)
                            existing_ids.append(contact['id'])

        # Format dates for JSON serialization
        for contact in contacts:
            # Format datetime fields
            if contact.get('last_message_time') and isinstance(contact['last_message_time'], datetime):
                contact['last_message_time'] = contact['last_message_time'].strftime('%Y-%m-%dT%H:%M:%S')

            # Ensure unread_count is a number
            if contact.get('unread_count') is None:
                contact['unread_count'] = 0

        cursor.close()
        conn.close()

        # Store in cache
        if not hasattr(api_contacts, 'cache'):
            api_contacts.cache = {}
        api_contacts.cache[cache_key] = (contacts, time.time())

        return jsonify(success=True, contacts=contacts)

    except Exception as e:
        print(f"Error fetching contacts for API: {e}")
        return jsonify(success=False, error=str(e), contacts=[])





@app.route('/api/genius/info')
@login_required
def api_genius_info():
    """API endpoint to get detailed information about the logged-in genius user"""
    # Check if user is a genius
    if session.get('user_type') != 'genius':
        return jsonify(success=False, error="Access denied. Only genius users can access this endpoint.")

    try:
        conn = get_db_connection()
        cursor = conn.cursor(dictionary=True)

        # Get genius information using the actual columns from approve_genius table
        query = """
            SELECT id, first_name, last_name, email, profile_photo, position,
                   hourly_rate, country, mobile, birthday, expertise,
                   availability, tax_id, introduction, professional_sum,
                   created_at
            FROM approve_genius
            WHERE id = %s
        """
        cursor.execute(query, (session.get('user_id'),))
        genius_data = cursor.fetchone()

        if not genius_data:
            return jsonify(success=False, error="Genius user not found.")

        # Get application statistics
        applications_query = """
            SELECT
                COUNT(*) as total_applications,
                SUM(CASE WHEN status = 'pending' THEN 1 ELSE 0 END) as pending_applications,
                SUM(CASE WHEN status = 'accepted' THEN 1 ELSE 0 END) as accepted_applications,
                SUM(CASE WHEN status = 'rejected' THEN 1 ELSE 0 END) as rejected_applications
            FROM applications
            WHERE genius_id = %s
        """
        cursor.execute(applications_query, (session.get('user_id'),))
        application_stats = cursor.fetchone()

        # Format dates for JSON serialization
        if genius_data.get('created_at'):
            genius_data['created_at'] = genius_data['created_at'].strftime('%Y-%m-%dT%H:%M:%S')
        if genius_data.get('birthday'):
            genius_data['birthday'] = genius_data['birthday'].strftime('%Y-%m-%d')

        # Handle profile_photo if it's binary data
        if genius_data.get('profile_photo') and isinstance(genius_data['profile_photo'], bytes):
            genius_data['profile_photo'] = None
            genius_data['has_profile_photo'] = True
        else:
            genius_data['has_profile_photo'] = False

        # Combine data
        result = {
            **genius_data,
            'application_stats': application_stats
        }

        cursor.close()
        conn.close()

        return jsonify(success=True, genius=result)

    except Exception as e:
        print(f"Error fetching genius info: {e}")
        return jsonify(success=False, error=str(e))

@app.route('/api/client/info')
@login_required
def api_client_info():
    """API endpoint to get detailed information about the logged-in client user"""
    # Check if user is a client
    if session.get('user_type') != 'client':
        return jsonify(success=False, error="Access denied. Only client users can access this endpoint.")

    try:
        conn = get_db_connection()
        cursor = conn.cursor(dictionary=True)

        # Get client information using the actual columns from approve_client table
        query = """
            SELECT id, first_name, last_name, work_email, profile_photo,
                   business_name, position, employee_count, industry,
                   country, mobile, birthday, business_logo, business_address,
                   business_email, business_website, introduction,
                   created_at, linkedin_id
            FROM approve_client
            WHERE id = %s
        """
        cursor.execute(query, (session.get('user_id'),))
        client_data = cursor.fetchone()

        if not client_data:
            return jsonify(success=False, error="Client user not found.")

        # Get job statistics
        jobs_query = """
            SELECT COUNT(*) as total_jobs
            FROM job_submissions
            WHERE client_id = %s
        """
        cursor.execute(jobs_query, (session.get('user_id'),))
        job_stats = cursor.fetchone()

        # Get application statistics for client's jobs
        applications_query = """
            SELECT
                COUNT(*) as total_applications,
                SUM(CASE WHEN a.status = 'pending' THEN 1 ELSE 0 END) as pending_applications,
                SUM(CASE WHEN a.status = 'accepted' THEN 1 ELSE 0 END) as accepted_applications,
                SUM(CASE WHEN a.status = 'rejected' THEN 1 ELSE 0 END) as rejected_applications
            FROM applications a
            JOIN job_submissions js ON a.job_id = js.id
            WHERE js.client_id = %s
        """
        cursor.execute(applications_query, (session.get('user_id'),))
        application_stats = cursor.fetchone()

        # Format dates for JSON serialization
        if client_data.get('created_at'):
            client_data['created_at'] = client_data['created_at'].strftime('%Y-%m-%dT%H:%M:%S')
        if client_data.get('birthday'):
            client_data['birthday'] = client_data['birthday'].strftime('%Y-%m-%d')

        # Handle profile_photo if it's binary data
        if client_data.get('profile_photo') and isinstance(client_data['profile_photo'], bytes):
            client_data['profile_photo'] = None
            client_data['has_profile_photo'] = True
        else:
            client_data['has_profile_photo'] = False

        # Handle business_logo if it's binary data
        if client_data.get('business_logo') and isinstance(client_data['business_logo'], bytes):
            client_data['business_logo'] = None
            client_data['has_business_logo'] = True
        else:
            client_data['has_business_logo'] = False

        # Combine data
        result = {
            **client_data,
            'job_stats': job_stats,
            'application_stats': application_stats
        }

        cursor.close()
        conn.close()

        return jsonify(success=True, client=result)

    except Exception as e:
        print(f"Error fetching client info: {e}")
        return jsonify(success=False, error=str(e))

@app.route('/api/client/jobs')
@login_required
def api_client_jobs():
    """API endpoint to get jobs posted by the logged-in client user"""
    # Check if user is a client
    if session.get('user_type') != 'client':
        return jsonify(success=False, error="Access denied. Only client users can access this endpoint.")

    try:
        conn = get_db_connection()
        cursor = conn.cursor(dictionary=True)

        # Get jobs posted by the client
        query = """
            SELECT js.id, js.title, js.description, js.project_size, js.duration,
                   js.budget_type, js.budget_amount, js.category, js.specialty,
                   js.created_at, js.job_type, js.gain,
                   (SELECT COUNT(*) FROM applications a WHERE a.job_id = js.id) as application_count
            FROM job_submissions js
            WHERE js.client_id = %s
            ORDER BY js.created_at DESC
        """
        cursor.execute(query, (session.get('user_id'),))
        jobs = cursor.fetchall()

        # Format dates for JSON serialization
        for job in jobs:
            if job.get('created_at'):
                job['created_at'] = job['created_at'].strftime('%Y-%m-%dT%H:%M:%S')

        cursor.close()
        conn.close()

        return jsonify(success=True, jobs=jobs)

    except Exception as e:
        print(f"Error fetching client jobs: {e}")
        return jsonify(success=False, error=str(e))

@app.route('/api/genius/applications')
@login_required
def api_genius_applications():
    """API endpoint to get applications submitted by the logged-in genius user"""
    # Check if user is a genius
    if session.get('user_type') != 'genius':
        return jsonify(success=False, error="Access denied. Only genius users can access this endpoint.")

    try:
        conn = get_db_connection()
        cursor = conn.cursor(dictionary=True)

        # Check if applications table exists
        check_table_query = """
            SELECT COUNT(*)
            FROM information_schema.tables
            WHERE table_schema = 'giggenius'
            AND table_name = 'applications'
        """
        cursor.execute(check_table_query)
        table_exists = cursor.fetchone()['COUNT(*)'] > 0

        applications = []

        if table_exists:
            # Get applications submitted by the genius with more job details
            query = """
                SELECT
                    a.id, a.genius_id, js.client_id, a.job_id,
                    a.position, a.status, a.created_at, a.updated_at,
                    js.title as job_title, js.description as job_description,
                    js.budget_type, js.budget_amount, js.project_size, js.duration,
                    js.category, js.specialty, js.job_type, js.gain,
                    ac.first_name as client_first_name, ac.last_name as client_last_name,
                    ac.business_name, ac.profile_photo as client_profile_photo,
                    ac.business_logo as client_business_logo, ac.industry as client_industry,
                    ac.business_website as client_website, ac.employee_count as client_employee_count
                FROM applications a
                JOIN job_submissions js ON a.job_id = js.id
                JOIN approve_client ac ON js.client_id = ac.id
                WHERE a.genius_id = %s
                ORDER BY a.created_at DESC
            """
            cursor.execute(query, (session.get('user_id'),))
            applications = cursor.fetchall()

            # Format dates for JSON serialization
            for app in applications:
                # Create copies of fields to modify to avoid changing dict size during iteration
                datetime_fields = {}
                binary_fields = {}
                additional_fields = {}

                # First identify all datetime and binary fields
                for key, value in app.items():
                    if isinstance(value, datetime):
                        datetime_fields[key] = value
                    elif isinstance(value, bytes):
                        binary_fields[key] = value

                # Then format datetime fields
                for key, value in datetime_fields.items():
                    app[key] = value.strftime('%Y-%m-%dT%H:%M:%S')

                # Handle binary data and prepare additional fields
                for key, value in binary_fields.items():
                    if key == 'client_profile_photo':
                        app[key] = None
                        additional_fields['client_has_profile_photo'] = True
                    elif key == 'client_business_logo':
                        app[key] = None
                        additional_fields['client_has_business_logo'] = True

                # Set default values for flags if binary data wasn't found
                if 'client_profile_photo' not in binary_fields:
                    additional_fields['client_has_profile_photo'] = False
                if 'client_business_logo' not in binary_fields:
                    additional_fields['client_has_business_logo'] = False

                # Add the additional fields
                for key, value in additional_fields.items():
                    app[key] = value

        cursor.close()
        conn.close()

        return jsonify(success=True, applications=applications)

    except Exception as e:
        print(f"Error fetching genius applications: {e}")
        return jsonify(success=False, error=str(e))

@app.route('/api/profile-photo/<string:user_type>/<int:user_id>')
def api_profile_photo(user_type, user_id):
    """API endpoint to get a user's profile photo"""
    conn = None
    cursor = None
    try:
        # Create a direct connection instead of using the connection pool
        # This avoids issues with connection pool exhaustion for image requests
        conn = mysql.connector.connect(**db_config)
        cursor = conn.cursor(dictionary=True)

        if user_type == 'genius':
            query = "SELECT profile_photo FROM approve_genius WHERE id = %s"
        elif user_type == 'client':
            query = "SELECT profile_photo FROM approve_client WHERE id = %s"
        else:
            return jsonify(success=False, error="Invalid user type. Must be 'genius' or 'client'."), 400

        cursor.execute(query, (user_id,))
        result = cursor.fetchone()

        if not result or not result['profile_photo']:
            # Return a default profile photo
            return redirect(url_for('static', filename='img/default-avatar.png'))

        # Store the profile photo in a variable before closing the connection
        profile_photo = result['profile_photo']

        # Return the profile photo as an image
        return send_file(
            io.BytesIO(profile_photo),
            mimetype='image/jpeg',
            as_attachment=False,
            download_name=f'{user_type}_{user_id}_profile.jpg'
        )

    except Exception as e:
        print(f"Error fetching profile photo: {e}")
        return redirect(url_for('static', filename='img/default-avatar.png'))
    finally:
        # Make sure to close cursor and connection in finally block
        if cursor:
            cursor.close()
        if conn:
            conn.close()

@app.route('/api/business-logo/<int:client_id>')
def api_business_logo(client_id):
    """API endpoint to get a client's business logo"""
    conn = None
    cursor = None
    try:
        # Create a direct connection instead of using the connection pool
        # This avoids issues with connection pool exhaustion for image requests
        conn = mysql.connector.connect(**db_config)
        cursor = conn.cursor(dictionary=True)

        query = "SELECT business_logo FROM approve_client WHERE id = %s"
        cursor.execute(query, (client_id,))
        result = cursor.fetchone()

        if not result or not result['business_logo']:
            # Return a default business logo
            return redirect(url_for('static', filename='img/default-business-logo.png'))

        # Store the business logo in a variable before closing the connection
        business_logo = result['business_logo']

        # Return the business logo as an image
        return send_file(
            io.BytesIO(business_logo),
            mimetype='image/jpeg',
            as_attachment=False,
            download_name=f'client_{client_id}_business_logo.jpg'
        )

    except Exception as e:
        print(f"Error fetching business logo: {e}")
        return redirect(url_for('static', filename='img/default-business-logo.png'))
    finally:
        # Make sure to close cursor and connection in finally block
        if cursor:
            cursor.close()
        if conn:
            conn.close()

@app.route('/api/client/applications')
@login_required
def api_client_applications():
    """API endpoint to get applications for jobs posted by the logged-in client user"""
    # Check if user is a client
    if session.get('user_type') != 'client':
        return jsonify(success=False, error="Access denied. Only client users can access this endpoint.")

    try:
        conn = get_db_connection()
        cursor = conn.cursor(dictionary=True)

        # Check if applications table exists
        check_table_query = """
            SELECT COUNT(*)
            FROM information_schema.tables
            WHERE table_schema = 'giggenius'
            AND table_name = 'applications'
        """
        cursor.execute(check_table_query)
        table_exists = cursor.fetchone()['COUNT(*)'] > 0

        applications = []

        if table_exists:
            # Get applications for client's jobs with more genius details
            query = """
                SELECT
                    a.id, a.genius_id, js.client_id, a.job_id,
                    ag.first_name, ag.last_name, ag.profile_photo,
                    a.position, a.status, a.created_at, a.updated_at,
                    js.title as job_title, js.description as job_description,
                    js.budget_type, js.budget_amount, js.project_size, js.duration,
                    js.category, js.specialty, js.job_type, js.gain,
                    ag.hourly_rate, ag.country, ag.email as genius_email,
                    ag.expertise, ag.availability, ag.introduction, ag.professional_sum
                FROM applications a
                JOIN job_submissions js ON a.job_id = js.id
                JOIN approve_genius ag ON a.genius_id = ag.id
                WHERE js.client_id = %s
                ORDER BY a.created_at DESC
            """
            cursor.execute(query, (session.get('user_id'),))
            applications = cursor.fetchall()

            # Format dates for JSON serialization
            for app in applications:
                # Create copies of fields to modify to avoid changing dict size during iteration
                datetime_fields = {}
                binary_fields = {}
                additional_fields = {}

                # First identify all datetime and binary fields
                for key, value in app.items():
                    if isinstance(value, datetime):
                        datetime_fields[key] = value
                    elif isinstance(value, bytes):
                        binary_fields[key] = value

                # Then format datetime fields
                for key, value in datetime_fields.items():
                    app[key] = value.strftime('%Y-%m-%dT%H:%M:%S')

                # Handle binary data and prepare additional fields
                for key, value in binary_fields.items():
                    if key == 'profile_photo':
                        app[key] = None
                        additional_fields['has_profile_photo'] = True

                # Set default values for flags if binary data wasn't found
                if 'profile_photo' not in binary_fields:
                    additional_fields['has_profile_photo'] = False

                # Add the additional fields
                for key, value in additional_fields.items():
                    app[key] = value

        cursor.close()
        conn.close()

        return jsonify(success=True, applications=applications)

    except Exception as e:
        print(f"Error fetching client applications: {e}")
        return jsonify(success=False, error=str(e))

@app.route('/api/user/<string:user_type>/<int:user_id>')
@login_required
def api_user_info(user_type, user_id):
    """API endpoint to get information about a specific user by ID and type"""
    try:
        conn = get_db_connection()
        cursor = conn.cursor(dictionary=True)

        if user_type == 'genius':
            # Get genius information with limited fields for privacy using actual columns
            query = """
                SELECT id, first_name, last_name, profile_photo, position,
                       hourly_rate, country, expertise, availability,
                       introduction, professional_sum
                FROM approve_genius
                WHERE id = %s
            """
            cursor.execute(query, (user_id,))
            user_data = cursor.fetchone()

            if not user_data:
                return jsonify(success=False, error="Genius user not found.")

            # Get application statistics if the requester is a client
            if session.get('user_type') == 'client':
                applications_query = """
                    SELECT
                        COUNT(*) as total_applications,
                        SUM(CASE WHEN a.status = 'pending' THEN 1 ELSE 0 END) as pending_applications,
                        SUM(CASE WHEN a.status = 'accepted' THEN 1 ELSE 0 END) as accepted_applications,
                        SUM(CASE WHEN a.status = 'rejected' THEN 1 ELSE 0 END) as rejected_applications
                    FROM applications a
                    JOIN job_submissions js ON a.job_id = js.id
                    WHERE a.genius_id = %s AND js.client_id = %s
                """
                cursor.execute(applications_query, (user_id, session.get('user_id')))
                application_stats = cursor.fetchone()
                user_data['application_stats'] = application_stats

        elif user_type == 'client':
            # Get client information with limited fields for privacy using actual columns
            query = """
                SELECT id, first_name, last_name, profile_photo,
                       business_name, position, employee_count, industry,
                       country, business_logo, business_website, introduction
                FROM approve_client
                WHERE id = %s
            """
            cursor.execute(query, (user_id,))
            user_data = cursor.fetchone()

            if not user_data:
                return jsonify(success=False, error="Client user not found.")

            # Get job statistics
            jobs_query = """
                SELECT COUNT(*) as total_jobs
                FROM job_submissions
                WHERE client_id = %s
            """
            cursor.execute(jobs_query, (user_id,))
            job_stats = cursor.fetchone()
            user_data['job_stats'] = job_stats

            # Get application statistics if the requester is a genius
            if session.get('user_type') == 'genius':
                applications_query = """
                    SELECT
                        COUNT(*) as total_applications,
                        SUM(CASE WHEN a.status = 'pending' THEN 1 ELSE 0 END) as pending_applications,
                        SUM(CASE WHEN a.status = 'accepted' THEN 1 ELSE 0 END) as accepted_applications,
                        SUM(CASE WHEN a.status = 'rejected' THEN 1 ELSE 0 END) as rejected_applications
                    FROM applications a
                    JOIN job_submissions js ON a.job_id = js.id
                    WHERE a.genius_id = %s AND js.client_id = %s
                """
                cursor.execute(applications_query, (session.get('user_id'), user_id))
                application_stats = cursor.fetchone()
                user_data['application_stats'] = application_stats
        else:
            return jsonify(success=False, error="Invalid user type. Must be 'genius' or 'client'.")

        # Handle profile_photo if it's binary data
        if user_data.get('profile_photo') and isinstance(user_data['profile_photo'], bytes):
            user_data['profile_photo'] = None
            user_data['has_profile_photo'] = True
        else:
            user_data['has_profile_photo'] = False

        # Handle business_logo if it's binary data (for client users)
        if user_type == 'client' and user_data.get('business_logo') and isinstance(user_data['business_logo'], bytes):
            user_data['business_logo'] = None
            user_data['has_business_logo'] = True
        elif user_type == 'client':
            user_data['has_business_logo'] = False

        cursor.close()
        conn.close()

        return jsonify(success=True, user_type=user_type, user=user_data)

    except Exception as e:
        print(f"Error fetching user info: {e}")
        return jsonify(success=False, error=str(e))

@app.route('/logout')
def logout():
    session.clear()
    return redirect(url_for('landing_page'))

@app.route('/chat')
@login_required
def chat():
    return render_template('chat.html')

@app.route('/messages')
@login_required
def messages():
    return render_template('chat.html')

@app.route('/socket-debug')
def socket_debug():
    """Debug route to check Socket.IO connection status"""
    # Get active Socket.IO sessions
    active_sessions = len(socketio.server.manager.rooms) if hasattr(socketio, 'server') else 0

    # Get active rooms
    active_rooms = {}
    if hasattr(socketio, 'server') and hasattr(socketio.server, 'manager'):
        for room_name, room in socketio.server.manager.rooms.items():
            if room_name.startswith('/'):  # Skip namespace rooms
                continue
            active_rooms[room_name] = list(room.keys())

    return jsonify({
        'success': True,
        'socket_io_status': 'running',
        'server_time': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
        'session_data': {
            'user_id': session.get('user_id'),
            'user_type': session.get('user_type')
        },
        'active_sessions': active_sessions,
        'active_rooms': active_rooms,
        'server_info': {
            'async_mode': socketio.async_mode,
            'cors_allowed_origins': socketio.cors_allowed_origins,
            'manage_session': socketio.manage_session
        }
    })

@app.route('/api/messages/<int:message_id>/reactions', methods=['GET', 'POST'])
@login_required
def message_reactions(message_id):
    """API endpoint to get or add reactions to a message"""
    user_id = session.get('user_id')
    user_type = session.get('user_type')

    if not user_id:
        return jsonify(success=False, error="User not authenticated")

    try:
        conn = get_db_connection()
        cursor = conn.cursor(dictionary=True)

        # Check if reactions table exists
        check_table_query = """
            SELECT COUNT(*)
            FROM information_schema.tables
            WHERE table_schema = 'giggenius'
            AND table_name = 'reactions'
        """
        cursor.execute(check_table_query)
        table_exists = cursor.fetchone()['COUNT(*)'] > 0

        if not table_exists:
            # Create reactions table
            create_table_query = """
                CREATE TABLE reactions (
                    id INT AUTO_INCREMENT PRIMARY KEY,
                    message_id INT NOT NULL,
                    user_id INT NOT NULL,
                    user_type VARCHAR(50) NOT NULL,
                    emoji VARCHAR(10) NOT NULL,
                    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                    INDEX (message_id),
                    INDEX (user_id),
                    UNIQUE KEY unique_reaction (message_id, user_id, emoji)
                )
            """
            cursor.execute(create_table_query)
            conn.commit()
            print("Created reactions table")

        # Handle GET request - get reactions for a message
        if request.method == 'GET':
            query = """
                SELECT r.*, m.sender_id, m.receiver_id
                FROM reactions r
                JOIN messages m ON r.message_id = m.id
                WHERE r.message_id = %s
                ORDER BY r.created_at
            """
            cursor.execute(query, (message_id,))
            reactions = cursor.fetchall()

            # Format the reactions
            formatted_reactions = []
            for reaction in reactions:
                formatted_reaction = dict(reaction)
                # Convert datetime objects to strings
                for key, value in formatted_reaction.items():
                    if isinstance(value, datetime):
                        formatted_reaction[key] = value.strftime('%Y-%m-%dT%H:%M:%S.000Z')
                formatted_reactions.append(formatted_reaction)

            cursor.close()
            conn.close()

            return jsonify(success=True, reactions=formatted_reactions)

        # Handle POST request - add a reaction
        elif request.method == 'POST':
            data = request.get_json()
            emoji = data.get('emoji')

            if not emoji:
                return jsonify(success=False, error="Emoji is required")

            # Check if the message exists and user has access to it
            check_message_query = """
                SELECT id, sender_id, receiver_id
                FROM messages
                WHERE id = %s
            """
            cursor.execute(check_message_query, (message_id,))
            message = cursor.fetchone()

            if not message:
                cursor.close()
                conn.close()
                return jsonify(success=False, error="Message not found")

            # Check if user is either sender or receiver
            if message['sender_id'] != user_id and message['receiver_id'] != user_id:
                cursor.close()
                conn.close()
                return jsonify(success=False, error="You don't have permission to react to this message")

            # Add or update reaction
            try:
                insert_query = """
                    INSERT INTO reactions (message_id, user_id, user_type, emoji)
                    VALUES (%s, %s, %s, %s)
                    ON DUPLICATE KEY UPDATE updated_at = CURRENT_TIMESTAMP
                """
                cursor.execute(insert_query, (message_id, user_id, user_type, emoji))
                conn.commit()

                # Get the reaction ID
                reaction_id = cursor.lastrowid

                # Get the updated reaction
                get_reaction_query = """
                    SELECT * FROM reactions WHERE id = %s
                """
                cursor.execute(get_reaction_query, (reaction_id,))
                reaction = cursor.fetchone()

                cursor.close()
                conn.close()

                # Format the reaction
                formatted_reaction = dict(reaction)
                # Convert datetime objects to strings
                for key, value in formatted_reaction.items():
                    if isinstance(value, datetime):
                        formatted_reaction[key] = value.strftime('%Y-%m-%dT%H:%M:%S.000Z')

                # Emit event via Socket.IO if available
                try:
                    socketio.emit('reaction_added', {
                        'message_id': message_id,
                        'reaction': formatted_reaction
                    }, room=f'user_{message["sender_id"]}')

                    socketio.emit('reaction_added', {
                        'message_id': message_id,
                        'reaction': formatted_reaction
                    }, room=f'user_{message["receiver_id"]}')
                except Exception as e:
                    print(f"Error emitting reaction_added event: {e}")

                return jsonify(success=True, reaction=formatted_reaction)

            except Exception as e:
                print(f"Error adding reaction: {e}")
                return jsonify(success=False, error=f"Error adding reaction: {str(e)}")

    except Exception as e:
        print(f"Error handling reaction: {e}")
        return jsonify(success=False, error=f"Error handling reaction: {str(e)}")

@app.route('/api/messages/batch/reactions', methods=['GET'])
@login_required
def get_batch_message_reactions():
    """API endpoint to get reactions for multiple messages in a single request (GET method)"""
    user_id = session.get('user_id')

    if not user_id:
        return jsonify(success=False, error="User not authenticated")

    try:
        # Get message IDs from query parameter
        message_ids_str = request.args.get('ids', '')
        if not message_ids_str:
            return jsonify(success=False, error="No message IDs provided")

        try:
            # Parse message IDs
            message_ids = [int(id_str) for id_str in message_ids_str.split(',') if id_str.strip()]

            # Limit to 50 messages at a time to prevent abuse
            message_ids = message_ids[:50]

            if not message_ids:
                return jsonify(success=False, error="Invalid message IDs")
        except ValueError:
            return jsonify(success=False, error="Invalid message ID format")

        conn = get_db_connection()
        cursor = conn.cursor(dictionary=True)

        # Check if reactions table exists
        check_table_query = """
            SELECT COUNT(*)
            FROM information_schema.tables
            WHERE table_schema = 'giggenius'
            AND table_name = 'reactions'
        """
        cursor.execute(check_table_query)
        table_exists = cursor.fetchone()['COUNT(*)'] > 0

        if not table_exists:
            cursor.close()
            conn.close()
            return jsonify(success=True, reactions={})

        # Get reactions for all messages in a single query
        placeholders = ', '.join(['%s'] * len(message_ids))
        query = f"""
            SELECT r.*, m.sender_id, m.receiver_id
            FROM reactions r
            JOIN messages m ON r.message_id = m.id
            WHERE r.message_id IN ({placeholders})
            ORDER BY r.message_id, r.created_at
        """
        cursor.execute(query, message_ids)
        reactions = cursor.fetchall()

        # Group reactions by message_id
        grouped_reactions = {}
        for reaction in reactions:
            message_id = reaction['message_id']
            if message_id not in grouped_reactions:
                grouped_reactions[message_id] = []

            # Format the reaction
            formatted_reaction = dict(reaction)
            # Convert datetime objects to strings
            for key, value in formatted_reaction.items():
                if isinstance(value, datetime):
                    formatted_reaction[key] = value.strftime('%Y-%m-%dT%H:%M:%S.000Z')

            grouped_reactions[message_id].append(formatted_reaction)

        cursor.close()
        conn.close()

        return jsonify(success=True, reactions=grouped_reactions)

    except Exception as e:
        print(f"Error handling batch reactions: {e}")
        return jsonify(success=False, error=f"Error handling batch reactions: {str(e)}")

@app.route('/api/batch/reactions', methods=['POST', 'GET'])
@login_required
def batch_reactions():
    """API endpoint to get reactions for multiple messages in a single request"""
    user_id = session.get('user_id')

    if not user_id:
        return jsonify(success=False, error="User not authenticated")

    try:
        # Get message IDs from request
        if request.method == 'POST':
            data = request.get_json()
            message_ids = data.get('message_ids', [])

            if not message_ids or not isinstance(message_ids, list):
                return jsonify(success=False, error="Valid message_ids array is required")
        else:  # GET method
            message_ids_str = request.args.get('ids', '')
            if not message_ids_str:
                return jsonify(success=False, error="Valid ids parameter is required")

            # Parse message IDs from comma-separated string
            try:
                message_ids = [int(id_str) for id_str in message_ids_str.split(',') if id_str.strip()]
            except ValueError:
                return jsonify(success=False, error="Invalid message IDs format")

            if not message_ids:
                return jsonify(success=False, error="No valid message IDs provided")

        # Limit the number of messages to prevent abuse
        if len(message_ids) > 50:
            message_ids = message_ids[:50]

        conn = get_db_connection()
        cursor = conn.cursor(dictionary=True)

        # Check if reactions table exists
        check_table_query = """
            SELECT COUNT(*)
            FROM information_schema.tables
            WHERE table_schema = 'giggenius'
            AND table_name = 'reactions'
        """
        cursor.execute(check_table_query)
        table_exists = cursor.fetchone()['COUNT(*)'] > 0

        if not table_exists:
            cursor.close()
            conn.close()
            return jsonify(success=True, reactions={})

        # Get reactions for all messages in a single query
        placeholders = ', '.join(['%s'] * len(message_ids))
        query = f"""
            SELECT r.*, m.sender_id, m.receiver_id
            FROM reactions r
            JOIN messages m ON r.message_id = m.id
            WHERE r.message_id IN ({placeholders})
            ORDER BY r.message_id, r.created_at
        """
        cursor.execute(query, message_ids)
        reactions = cursor.fetchall()

        # Group reactions by message_id
        grouped_reactions = {}
        for reaction in reactions:
            message_id = reaction['message_id']
            if message_id not in grouped_reactions:
                grouped_reactions[message_id] = []

            # Format the reaction
            formatted_reaction = dict(reaction)
            # Convert datetime objects to strings
            for key, value in formatted_reaction.items():
                if isinstance(value, datetime):
                    formatted_reaction[key] = value.strftime('%Y-%m-%dT%H:%M:%S.000Z')

            grouped_reactions[message_id].append(formatted_reaction)

        cursor.close()
        conn.close()

        return jsonify(success=True, reactions=grouped_reactions)

    except Exception as e:
        print(f"Error handling batch reactions: {e}")
        return jsonify(success=False, error=f"Error handling batch reactions: {str(e)}")

@app.route('/api/messages/<int:message_id>/reactions/<string:emoji>', methods=['DELETE'])
@login_required
def delete_reaction(message_id, emoji):
    """API endpoint to delete a reaction from a message"""
    user_id = session.get('user_id')

    if not user_id:
        return jsonify(success=False, error="User not authenticated")

    try:
        conn = get_db_connection()
        cursor = conn.cursor(dictionary=True)

        # Check if the reaction exists
        check_query = """
            SELECT r.*, m.sender_id, m.receiver_id
            FROM reactions r
            JOIN messages m ON r.message_id = m.id
            WHERE r.message_id = %s AND r.user_id = %s AND r.emoji = %s
        """
        cursor.execute(check_query, (message_id, user_id, emoji))
        reaction = cursor.fetchone()

        if not reaction:
            cursor.close()
            conn.close()
            return jsonify(success=False, error="Reaction not found")

        # Delete the reaction
        delete_query = """
            DELETE FROM reactions
            WHERE message_id = %s AND user_id = %s AND emoji = %s
        """
        cursor.execute(delete_query, (message_id, user_id, emoji))
        conn.commit()

        cursor.close()
        conn.close()

        # Emit event via Socket.IO if available
        try:
            socketio.emit('reaction_removed', {
                'message_id': message_id,
                'user_id': user_id,
                'emoji': emoji
            }, room=f'user_{reaction["sender_id"]}')

            socketio.emit('reaction_removed', {
                'message_id': message_id,
                'user_id': user_id,
                'emoji': emoji
            }, room=f'user_{reaction["receiver_id"]}')
        except Exception as e:
            print(f"Error emitting reaction_removed event: {e}")

        return jsonify(success=True)

    except Exception as e:
        print(f"Error deleting reaction: {e}")
        return jsonify(success=False, error=f"Error deleting reaction: {str(e)}")

@app.route('/api/mark_messages_read/<int:contact_id>', methods=['POST'])
@login_required
def mark_messages_read_api(contact_id):
    """REST API endpoint to mark messages as read (fallback for when Socket.IO fails)"""
    user_id = session.get('user_id')
    conn = None
    cursor = None

    if not user_id or not contact_id:
        return jsonify(success=False, error="Invalid data")

    try:
        # Get a database connection with our improved connection manager
        print(f"API: Marking messages as read from {contact_id} to {user_id}")
        conn = get_db_connection()

        if not conn:
            raise RuntimeError("Could not establish database connection")

        cursor = conn.cursor(dictionary=True)

        # Update messages where current user is the receiver and the sender is the contact
        update_query = """
            UPDATE messages
            SET is_read = TRUE
            WHERE receiver_id = %s AND sender_id = %s AND is_read = FALSE
        """
        cursor.execute(update_query, (user_id, contact_id))
        conn.commit()

        rows_affected = cursor.rowcount
        print(f"API: Marked {rows_affected} messages as read")

        # Get updated unread count for this contact
        unread_query = """
            SELECT COUNT(*) as unread_count
            FROM messages
            WHERE receiver_id = %s AND sender_id = %s AND is_read = FALSE
        """
        cursor.execute(unread_query, (user_id, contact_id))
        unread_result = cursor.fetchone()
        unread_count = unread_result['unread_count'] if unread_result else 0

        # Get the updated messages to reflect read status
        messages_query = """
            SELECT
                id, sender_id, sender_type, receiver_id, receiver_type,
                message_text as message, is_read, is_auto,
                related_to_job_id, related_to_application_id,
                message_type, timestamp, status
            FROM messages
            WHERE (sender_id = %s AND receiver_id = %s)
               OR (sender_id = %s AND receiver_id = %s)
            ORDER BY timestamp DESC
            LIMIT 10
        """
        cursor.execute(messages_query, (user_id, contact_id, contact_id, user_id))
        messages = cursor.fetchall()

        # Format dates for JSON serialization
        formatted_messages = []
        for msg in messages:
            formatted_msg = dict(msg)
            # Format datetime fields
            for key, value in formatted_msg.items():
                if isinstance(value, datetime):
                    formatted_msg[key] = value.strftime('%Y-%m-%dT%H:%M:%S.000Z')
            formatted_messages.append(formatted_msg)

        # Try to emit a Socket.IO event as well if possible
        try:
            socketio.emit('messages_marked_read', {
                'contact_id': contact_id,
                'messages_read': rows_affected,
                'unread_count': unread_count,
                'messages': formatted_messages
            }, room=str(user_id))
        except Exception as socket_err:
            print(f"Could not emit Socket.IO event from REST API: {socket_err}")

        return jsonify(
            success=True,
            contact_id=contact_id,
            messages_read=rows_affected,
            unread_count=unread_count,
            messages=formatted_messages
        )

    except Exception as e:
        print(f"Error marking messages as read via API: {e}")
        error_details = str(e)
        if 'MySQL Connection not available' in error_details:
            error_details += " - Connection pool may be exhausted or database server is unreachable"

        return jsonify(success=False, error=f"Error marking messages as read: {error_details}")
    finally:
        # Ensure resources are properly closed
        if cursor:
            try:
                cursor.close()
            except:
                pass
        if conn:
            try:
                # Use our improved release_connection method
                conn_manager.release_connection(conn)
            except Exception as e:
                print(f"Error releasing connection in API: {e}")
                try:
                    conn.close()
                except:
                    pass

@app.route('/client_jobs')
@login_required
def client_jobs():
    """Display jobs posted by the logged-in client"""
    # Check if user is a client
    if session.get('user_type') != 'client':
        return redirect(url_for('landing_page'))

    try:
        conn = get_db_connection()
        cursor = conn.cursor(dictionary=True)

        # Get jobs posted by the client
        query = """
            SELECT js.id, js.title, js.description, js.project_size, js.duration,
                   js.budget_type, js.budget_amount, js.category, js.specialty,
                   js.created_at, js.job_type, js.gain,
                   (SELECT COUNT(*) FROM applications a WHERE a.job_id = js.id) as application_count
            FROM job_submissions js
            WHERE js.client_id = %s
            ORDER BY js.created_at DESC
        """
        cursor.execute(query, (session.get('user_id'),))
        jobs = cursor.fetchall()

        # Format dates for display
        for job in jobs:
            if job.get('created_at'):
                job['created_at_formatted'] = job['created_at'].strftime('%Y-%m-%d %H:%M:%S')

        cursor.close()
        conn.close()

        return render_template('client_jobs.html', jobs=jobs)

    except Exception as e:
        print(f"Error fetching client jobs: {e}")
        return render_template('client_jobs.html', jobs=[], error=str(e))



# Placeholder routes for landing page links
@app.route('/find_geniuses')
def find_geniuses():
    return render_template('find_geniuses.html')

@app.route('/api/genius/<int:genius_id>/introduction')
@login_required
def api_genius_introduction(genius_id):
    """API endpoint to get the full introduction for a genius"""
    try:
        conn = get_db_connection()
        cursor = conn.cursor(dictionary=True)

        # Get the full introduction for the genius
        query = """
            SELECT introduction
            FROM approve_genius
            WHERE id = %s
        """
        cursor.execute(query, (genius_id,))
        result = cursor.fetchone()

        cursor.close()
        conn.close()

        if result and result['introduction']:
            return jsonify(success=True, introduction=result['introduction'])
        else:
            return jsonify(success=False, error="Introduction not found", introduction="No introduction available.")

    except Exception as e:
        print(f"Error fetching genius introduction: {e}")
        return jsonify(success=False, error=str(e), introduction="Error loading introduction.")


@app.route('/update_genius_profile', methods=['POST'])
@login_required
def update_genius_profile():
    """Update genius profile information"""
    if session.get('user_type') != 'genius':
        return jsonify(success=False, error="Unauthorized access")

    try:
        conn = get_db_connection()
        cursor = conn.cursor()

        user_id = session.get('user_id')

        # Handle both JSON and FormData requests
        if request.content_type and 'application/json' in request.content_type:
            data = request.get_json()
            profile_photo = None
        else:
            # FormData request (with potential file upload)
            data = request.form.to_dict()
            profile_photo = request.files.get('profile_photo')
            if profile_photo and profile_photo.filename:
                profile_photo = profile_photo.read()
            else:
                profile_photo = None

        # Build update query dynamically based on provided fields
        update_fields = []
        values = []

        # Map form fields to database columns
        field_mapping = {
            'email': 'email',
            'mobile': 'mobile',
            'position': 'position',
            'expertise': 'expertise',
            'hourly_rate': 'hourly_rate',
            'availability': 'availability',
            'country': 'country',
            'language': 'language'
        }

        for form_field, db_field in field_mapping.items():
            if form_field in data and data[form_field] is not None:
                update_fields.append(f"{db_field} = %s")
                # Convert hourly_rate to float if needed
                if form_field == 'hourly_rate':
                    values.append(float(data[form_field]))
                else:
                    values.append(data[form_field])

        # Handle profile photo if provided
        if profile_photo:
            update_fields.append("profile_photo = %s")
            values.append(profile_photo)

        if not update_fields:
            return jsonify(success=False, error="No fields to update")

        values.append(user_id)

        query = f"""
            UPDATE approve_genius
            SET {', '.join(update_fields)}
            WHERE id = %s
        """

        cursor.execute(query, values)
        conn.commit()

        cursor.close()
        conn.close()

        return jsonify(success=True, message="Profile updated successfully")

    except Exception as e:
        print(f"Error updating genius profile: {e}")
        return jsonify(success=False, error=str(e))


@app.route('/introduction', methods=['POST'])
@login_required
def update_introduction():
    """Update genius introduction"""
    if session.get('user_type') != 'genius':
        return jsonify(success=False, error="Unauthorized access")

    try:
        data = request.get_json()
        introduction = data.get('introduction', '')

        conn = get_db_connection()
        cursor = conn.cursor()

        query = """
            UPDATE approve_genius
            SET introduction = %s
            WHERE id = %s
        """

        cursor.execute(query, (introduction, session.get('user_id')))
        conn.commit()

        cursor.close()
        conn.close()

        return jsonify(success=True, message="Introduction updated successfully")

    except Exception as e:
        print(f"Error updating introduction: {e}")
        return jsonify(success=False, error=str(e))


@app.route('/update_professional_summary', methods=['POST'])
@login_required
def update_professional_summary():
    """Update genius professional summary"""
    if session.get('user_type') != 'genius':
        return jsonify(success=False, error="Unauthorized access")

    try:
        data = request.get_json()
        professional_summary = data.get('professional_summary', '')

        conn = get_db_connection()
        cursor = conn.cursor()

        query = """
            UPDATE approve_genius
            SET professional_sum = %s
            WHERE id = %s
        """

        cursor.execute(query, (professional_summary, session.get('user_id')))
        conn.commit()

        cursor.close()
        conn.close()

        return jsonify(success=True, message="Professional summary updated successfully")

    except Exception as e:
        print(f"Error updating professional summary: {e}")
        return jsonify(success=False, error=str(e))


@app.route('/api/genius/hourly_rate')
@login_required
def api_genius_hourly_rate():
    """Get current user's hourly rate"""
    if session.get('user_type') != 'genius':
        return jsonify(success=False, error="Unauthorized access")

    try:
        conn = get_db_connection()
        cursor = conn.cursor(dictionary=True)

        query = """
            SELECT hourly_rate
            FROM approve_genius
            WHERE id = %s
        """

        cursor.execute(query, (session.get('user_id'),))
        result = cursor.fetchone()

        cursor.close()
        conn.close()

        if result:
            return jsonify(success=True, hourly_rate=result['hourly_rate'] or 0)
        else:
            return jsonify(success=False, error="User not found", hourly_rate=0)

    except Exception as e:
        print(f"Error fetching hourly rate: {e}")
        return jsonify(success=False, error=str(e), hourly_rate=0)


@app.route('/api/genius/professional_summary')
@login_required
def api_genius_professional_summary():
    """Get current user's professional summary"""
    if session.get('user_type') != 'genius':
        return jsonify(success=False, error="Unauthorized access")

    try:
        conn = get_db_connection()
        cursor = conn.cursor(dictionary=True)

        query = """
            SELECT professional_sum
            FROM approve_genius
            WHERE id = %s
        """

        cursor.execute(query, (session.get('user_id'),))
        result = cursor.fetchone()

        cursor.close()
        conn.close()

        if result:
            return jsonify(success=True, professional_sum=result['professional_sum'])
        else:
            return jsonify(success=False, error="User not found")

    except Exception as e:
        print(f"Error fetching professional summary: {e}")
        return jsonify(success=False, error=str(e))



@app.route('/api/genius/profile_data')
@login_required
def api_genius_profile_data():
    """Get current user's profile data for edit form"""
    if session.get('user_type') != 'genius':
        return jsonify(success=False, error="Unauthorized access")

    try:
        conn = get_db_connection()
        cursor = conn.cursor(dictionary=True)

        query = """
            SELECT email, mobile, position, expertise, hourly_rate, availability, country, language
            FROM approve_genius
            WHERE id = %s
        """

        cursor.execute(query, (session.get('user_id'),))
        result = cursor.fetchone()

        cursor.close()
        conn.close()

        if result:
            return jsonify(success=True, profile_data=result)
        else:
            return jsonify(success=False, error="User not found")

    except Exception as e:
        print(f"Error fetching profile data: {e}")
        return jsonify(success=False, error=str(e))


@app.route('/api/genius/profile_info')
@login_required
def api_genius_profile_info():
    """Get current user's basic profile information for display"""
    if session.get('user_type') != 'genius':
        return jsonify(success=False, error="Unauthorized access")

    try:
        conn = get_db_connection()
        cursor = conn.cursor(dictionary=True)

        query = """
            SELECT first_name, last_name, position, country
            FROM approve_genius
            WHERE id = %s
        """

        cursor.execute(query, (session.get('user_id'),))
        result = cursor.fetchone()

        cursor.close()
        conn.close()

        if result:
            return jsonify(
                success=True,
                first_name=result['first_name'] or '',
                last_name=result['last_name'] or '',
                position=result['position'] or 'Freelancer',
                country=result['country'] or 'Philippines',
            )
        else:
            return jsonify(
                success=False,
                error="User not found",
                first_name='',
                last_name='',
                position='Freelancer',
                country='Philippines'
            )

    except Exception as e:
        print(f"Error fetching profile info: {e}")
        return jsonify(
            success=False,
            error=str(e),
            first_name='',
            last_name='',
            position='Freelancer',
            country='Philippines'
        )

@app.route('/api/geniuses')
@login_required
def api_geniuses():
    """API endpoint to get genius profiles from the approve_genius table"""
    try:
        # Get filter parameters
        country = request.args.get('country', '')
        expertise = request.args.get('expertise', '')
        availability = request.args.get('availability', '')
        hourly_rate_min = request.args.get('hourly_rate_min', 0, type=int)
        hourly_rate_max = request.args.get('hourly_rate_max', 1000, type=int)

        conn = get_db_connection()
        cursor = conn.cursor(dictionary=True)

        # Build the query with filters
        query = """
            SELECT id, profile_photo, first_name, last_name, email,
                   country, position, expertise, hourly_rate, availability,
                   introduction, professional_sum
            FROM approve_genius
            WHERE 1=1
        """

        params = []

        if country:
            query += " AND country = %s"
            params.append(country)

        if expertise:
            query += " AND expertise = %s"
            params.append(expertise)

        if availability:
            query += " AND availability = %s"
            params.append(availability)

        query += " AND hourly_rate BETWEEN %s AND %s"
        params.extend([hourly_rate_min, hourly_rate_max])

        query += " ORDER BY id DESC LIMIT 50"

        cursor.execute(query, params)
        geniuses = cursor.fetchall()

        # Process the results
        for genius in geniuses:
            # Handle binary data
            if genius.get('profile_photo') and isinstance(genius['profile_photo'], bytes):
                genius['has_profile_photo'] = True
                genius['profile_photo'] = None
            else:
                genius['has_profile_photo'] = False

            # Format introduction and professional_sum
            if genius.get('introduction'):
                genius['introduction'] = genius['introduction'][:150] + '...' if len(genius['introduction']) > 150 else genius['introduction']

            if genius.get('professional_sum'):
                genius['professional_sum'] = genius['professional_sum'][:150] + '...' if len(genius['professional_sum']) > 150 else genius['professional_sum']

        cursor.close()
        conn.close()

        return jsonify(success=True, geniuses=geniuses)

    except Exception as e:
        print(f"Error fetching genius profiles: {e}")
        return jsonify(success=False, error=str(e))

@app.route('/find_gigs')
def find_gigs():
    return render_template('find_gigs.html')

@app.route('/allgigpost')
@login_required
def allgigpost():
    # Allow both clients and geniuses to view all posted jobs
    if session.get('user_type') not in ['client', 'genius']:
        return redirect(url_for('landing_page'))

    try:
        # Get pagination parameters
        page = request.args.get('page', 1, type=int)
        per_page = 10  # Jobs per page
        offset = (page - 1) * per_page

        conn = get_db_connection()
        cursor = conn.cursor(dictionary=True)

        # First, let's check what status values actually exist in the database
        status_check_query = """
            SELECT DISTINCT status, COUNT(*) as count
            FROM job_submissions
            GROUP BY status
            ORDER BY count DESC
        """
        cursor.execute(status_check_query)
        status_results = cursor.fetchall()
        print("=== CURRENT STATUS VALUES IN DATABASE ===")
        for result in status_results:
            print(f"Status: '{result['status']}' - Count: {result['count']}")
        print("===========================================")

        # Get jobs based on user type
        user_type = session.get('user_type')
        user_id = session.get('user_id')

        if user_type == 'client':
            # First get total count for pagination
            count_query = """
                SELECT COUNT(*) as total
                FROM job_submissions js
                WHERE js.client_id = %s
            """
            cursor.execute(count_query, (user_id,))
            total_jobs = cursor.fetchone()['total']

            # For clients: Show only their own jobs (including drafts and published)
            query = """
                SELECT js.id, js.title, js.description, js.project_size, js.duration,
                       js.budget_type, js.budget_amount, js.category, js.specialty, js.skills,
                       js.created_at, js.job_type, js.gain, js.client_id, js.status,
                       ac.first_name as client_first_name, ac.last_name as client_last_name,
                       ac.business_name, ac.country as client_country, ac.position as client_position,
                       (SELECT COUNT(*) FROM applications a WHERE a.job_id = js.id) as proposals_count,
                       (SELECT COUNT(*) FROM applications a WHERE a.job_id = js.id AND a.created_at > DATE_SUB(NOW(), INTERVAL 7 DAY)) as new_proposals_count,
                       (SELECT COUNT(*) FROM applications a WHERE a.job_id = js.id AND a.status = 'Messaged') as messaged_count,
                       (SELECT COUNT(*) FROM applications a WHERE a.job_id = js.id AND a.status = 'Hired') as hired_count,
                       js.status as actual_status,
                       CASE
                           WHEN js.status = 'draft' THEN 'draft'
                           WHEN js.status = 'publish' THEN 'publish'
                           WHEN js.status = 'published' THEN 'published'
                           WHEN js.status = 'open' THEN 'open'
                           WHEN js.status = 'closed' THEN 'closed'
                           WHEN js.status = 'paused' THEN 'paused'
                           WHEN EXISTS (SELECT 1 FROM applications a WHERE a.job_id = js.id AND a.status = 'Hired') THEN 'filled'
                           WHEN js.created_at < DATE_SUB(NOW(), INTERVAL 30 DAY) AND js.status != 'draft' THEN 'expired'
                           ELSE js.status
                       END as display_status,
                       DATE_FORMAT(js.created_at, '%b %d, %Y') as status_date
                FROM job_submissions js
                JOIN approve_client ac ON js.client_id = ac.id
                WHERE js.client_id = %s
                ORDER BY js.created_at DESC
                LIMIT %s OFFSET %s
            """
            cursor.execute(query, (user_id, per_page, offset))
        else:
            # First get total count for pagination
            count_query = """
                SELECT COUNT(*) as total
                FROM job_submissions js
                WHERE js.status IN ('publish', 'published', 'open')
            """
            cursor.execute(count_query)
            total_jobs = cursor.fetchone()['total']

            # For genius users: Show only published/open jobs from all clients (no drafts)
            query = """
                SELECT js.id, js.title, js.description, js.project_size, js.duration,
                       js.budget_type, js.budget_amount, js.category, js.specialty, js.skills,
                       js.created_at, js.job_type, js.gain, js.client_id, js.status,
                       ac.first_name as client_first_name, ac.last_name as client_last_name,
                       ac.business_name, ac.country as client_country, ac.position as client_position,
                       (SELECT COUNT(*) FROM applications a WHERE a.job_id = js.id) as proposals_count,
                       (SELECT COUNT(*) FROM applications a WHERE a.job_id = js.id AND a.created_at > DATE_SUB(NOW(), INTERVAL 7 DAY)) as new_proposals_count,
                       (SELECT COUNT(*) FROM applications a WHERE a.job_id = js.id AND a.status = 'Messaged') as messaged_count,
                       (SELECT COUNT(*) FROM applications a WHERE a.job_id = js.id AND a.status = 'Hired') as hired_count,
                       js.status as actual_status,
                       CASE
                           WHEN js.status = 'draft' THEN 'draft'
                           WHEN js.status = 'publish' THEN 'publish'
                           WHEN js.status = 'published' THEN 'published'
                           WHEN js.status = 'open' THEN 'open'
                           WHEN js.status = 'closed' THEN 'closed'
                           WHEN js.status = 'paused' THEN 'paused'
                           WHEN EXISTS (SELECT 1 FROM applications a WHERE a.job_id = js.id AND a.status = 'Hired') THEN 'filled'
                           WHEN js.created_at < DATE_SUB(NOW(), INTERVAL 30 DAY) AND js.status != 'draft' THEN 'expired'
                           ELSE js.status
                       END as display_status,
                       DATE_FORMAT(js.created_at, '%b %d, %Y') as status_date
                FROM job_submissions js
                JOIN approve_client ac ON js.client_id = ac.id
                WHERE js.status IN ('publish', 'published', 'open')
                ORDER BY js.created_at DESC
                LIMIT %s OFFSET %s
            """
            cursor.execute(query, (per_page, offset))
        job_posts = cursor.fetchall()

        # Format data for display
        for job in job_posts:
            # Use the actual database status and display status
            job['actual_status'] = job.get('actual_status', job.get('status', 'draft'))
            job['status'] = job.get('display_status', job.get('status', 'draft'))

            # Format created_at date
            if job.get('created_at'):
                job['created_at'] = job['created_at']

            # Add client display name
            if job.get('business_name'):
                job['client_display_name'] = job['business_name']
            else:
                job['client_display_name'] = f"{job.get('client_first_name', '')} {job.get('client_last_name', '')}".strip()

            # Format budget display
            if job.get('budget_amount'):
                if job.get('budget_type') == 'hourly':
                    job['budget_display'] = f"${job['budget_amount']}/hr"
                else:
                    job['budget_display'] = f"${job['budget_amount']}"
            else:
                job['budget_display'] = "Budget not specified"

            # Check if current user owns this job (for edit permissions)
            job['is_owner'] = (job.get('client_id') == session.get('user_id'))

            # Add status labels for display
            if job['actual_status'] == 'draft':
                job['status_label'] = 'Draft'
                job['status_description'] = 'Not yet published'
            elif job['actual_status'] == 'published':
                job['status_label'] = 'Published'
                job['status_description'] = 'Live and accepting applications'
            elif job['actual_status'] == 'open':
                job['status_label'] = 'Open'
                job['status_description'] = 'Accepting applications'
            elif job['actual_status'] == 'closed':
                job['status_label'] = 'Closed'
                job['status_description'] = 'No longer accepting applications'
            elif job['actual_status'] == 'paused':
                job['status_label'] = 'Paused'
                job['status_description'] = 'Temporarily paused'
            else:
                job['status_label'] = job['actual_status'].title()
                job['status_description'] = f"Status: {job['actual_status']}"

        # Calculate pagination info
        total_pages = (total_jobs + per_page - 1) // per_page  # Ceiling division
        has_prev = page > 1
        has_next = page < total_pages

        # Calculate showing range
        start_item = offset + 1 if total_jobs > 0 else 0
        end_item = min(offset + per_page, total_jobs)

        pagination = {
            'page': page,
            'per_page': per_page,
            'total': total_jobs,
            'total_pages': total_pages,
            'has_prev': has_prev,
            'has_next': has_next,
            'prev_num': page - 1 if has_prev else None,
            'next_num': page + 1 if has_next else None,
            'start_item': start_item,
            'end_item': end_item
        }

        cursor.close()
        conn.close()

        return render_template('allgigpost.html', job_posts=job_posts, pagination=pagination)

    except Exception as e:
        print(f"Error fetching job posts: {e}")
        # Return empty pagination data on error
        empty_pagination = {
            'page': 1,
            'per_page': 10,
            'total': 0,
            'total_pages': 0,
            'has_prev': False,
            'has_next': False,
            'prev_num': None,
            'next_num': None,
            'start_item': 0,
            'end_item': 0
        }
        return render_template('allgigpost.html', job_posts=[], pagination=empty_pagination)

@app.route('/job_details_api/<int:job_id>')
@login_required
def job_details_api(job_id):
    """API endpoint to get detailed job information"""
    try:
        conn = get_db_connection()
        cursor = conn.cursor(dictionary=True)

        # Get job details with client information
        query = """
            SELECT js.id, js.title, js.description, js.project_size, js.project_description,
                   js.duration, js.experience_level, js.budget_type,
                   js.budget_amount, js.category, js.specialty, js.skills, js.job_type,
                   js.created_at, js.status,
                   ac.id as client_id, ac.first_name as client_first_name,
                   ac.last_name as client_last_name, ac.business_name, ac.country as client_country,
                   ac.position as client_position, ac.industry, ac.business_website,
                   ac.employee_count
            FROM job_submissions js
            JOIN approve_client ac ON js.client_id = ac.id
            WHERE js.id = %s AND js.status = 'publish'
        """
        cursor.execute(query, (job_id,))
        job = cursor.fetchone()

        if not job:
            cursor.close()
            conn.close()
            return jsonify(success=False, error="Job not found or not published")

        # Get client rating (you might want to add a ratings table later)
        job['client_rating'] = 4.5  # Default rating for now

        # Parse skills if it's a JSON string
        if job.get('skills') and isinstance(job['skills'], str):
            try:
                job['skills_list'] = json.loads(job['skills'])
            except json.JSONDecodeError:
                job['skills_list'] = []
        else:
            job['skills_list'] = []

        # Format budget display
        if job.get('budget_amount'):
            if job.get('budget_type') == 'hourly':
                job['budget_display'] = f"${job['budget_amount']}/hr"
            else:
                job['budget_display'] = f"${job['budget_amount']}"
        else:
            job['budget_display'] = "Budget not specified"

        # Format dates for JSON serialization
        if job.get('created_at'):
            job['created_at'] = job['created_at'].strftime('%Y-%m-%d %H:%M:%S')

        # Create client object
        client = {
            'id': job['client_id'],
            'first_name': job['client_first_name'],
            'last_name': job['client_last_name'],
            'business_name': job['business_name'],
            'country': job['client_country'],
            'position': job['client_position'],
            'industry': job['industry'],
            'business_website': job['business_website'],
            'employee_count': job['employee_count'],
            'rating': job['client_rating']
        }

        cursor.close()
        conn.close()

        return jsonify(success=True, job=job, client=client)

    except Exception as e:
        print(f"Error fetching job details: {e}")
        return jsonify(success=False, error=str(e))

@app.route('/my_application')
@login_required
def my_application():
    """API endpoint to get applications for the current genius user"""
    # Only geniuses can access this
    if session.get('user_type') != 'genius':
        return jsonify(success=False, error="Access denied")

    try:
        conn = get_db_connection()
        cursor = conn.cursor(dictionary=True)

        # Check if applications table exists
        check_table_query = """
            SELECT COUNT(*)
            FROM information_schema.tables
            WHERE table_schema = 'giggenius'
            AND table_name = 'applications'
        """
        cursor.execute(check_table_query)
        table_exists = cursor.fetchone()['COUNT(*)'] > 0

        applications = []

        if table_exists:
            # Get applications for the current genius
            query = """
                SELECT
                    a.id, a.genius_id, js.client_id, a.job_id,
                    a.position, a.status, a.created_at as applied_date,
                    js.title as job_title, js.description as job_description,
                    js.budget_type, js.budget_amount, js.project_size, js.duration,
                    js.category, js.specialty, js.job_type,
                    ac.first_name as client_first_name, ac.last_name as client_last_name,
                    ac.business_name as company_name, ac.country as location
                FROM applications a
                JOIN job_submissions js ON a.job_id = js.id
                JOIN approve_client ac ON js.client_id = ac.id
                WHERE a.genius_id = %s
                ORDER BY a.created_at DESC
            """
            cursor.execute(query, (session.get('user_id'),))
            applications = cursor.fetchall()

            # Format dates and add additional fields
            for app in applications:
                if app.get('applied_date'):
                    app['applied_date'] = app['applied_date'].strftime('%Y-%m-%d')
                
                # Add proposed rate (you might want to add this to the applications table)
                app['proposed_rate'] = 50  # Default for now
                
                # Use location from job or client
                if not app.get('location'):
                    app['location'] = 'Remote'

        cursor.close()
        conn.close()

        return jsonify(success=True, applications=applications)

    except Exception as e:
        print(f"Error fetching applications: {e}")
        return jsonify(success=False, error=str(e), applications=[])

@app.route('/apply_to_job/<int:job_id>', methods=['POST'])
@login_required
def apply_to_job(job_id):
    """API endpoint to apply to a job"""
    # Only geniuses can apply for jobs
    if session.get('user_type') != 'genius':
        return jsonify(success=False, error="Only geniuses can apply for jobs")

    try:
        conn = get_db_connection()
        cursor = conn.cursor(dictionary=True)

        # Check if already applied
        check_query = """
            SELECT id FROM applications
            WHERE genius_id = %s AND job_id = %s
        """
        cursor.execute(check_query, (session.get('user_id'), job_id))
        existing_application = cursor.fetchone()

        if existing_application:
            cursor.close()
            conn.close()
            return jsonify(success=False, error="You have already applied for this job")

        # Get the client_id from the job_submissions table
        client_query = """
            SELECT client_id FROM job_submissions WHERE id = %s
        """
        cursor.execute(client_query, (job_id,))
        client_result = cursor.fetchone()

        if not client_result:
            cursor.close()
            conn.close()
            return jsonify(success=False, error="Job not found")

        client_id = client_result['client_id']

        # Get genius information
        genius_query = """
            SELECT first_name, last_name, profile_photo, position
            FROM approve_genius
            WHERE id = %s
        """
        cursor.execute(genius_query, (session.get('user_id'),))
        genius_info = cursor.fetchone()

        first_name = genius_info['first_name'] if genius_info and genius_info['first_name'] else session.get('first_name')
        last_name = genius_info['last_name'] if genius_info and genius_info['last_name'] else session.get('last_name')
        profile_photo = genius_info['profile_photo'] if genius_info and len(genius_info) > 2 else None
        position = genius_info['position'] if genius_info and len(genius_info) > 3 else None

        # Insert the application
        query = """
            INSERT INTO applications (
                genius_id, client_id, job_id, first_name, last_name,
                profile_photo, position, status, created_at, updated_at
            ) VALUES (%s, %s, %s, %s, %s, %s, %s, %s, NOW(), NOW())
        """
        cursor.execute(query, (
            session.get('user_id'),
            client_id,
            job_id,
            first_name,
            last_name,
            profile_photo,
            position,
            'pending'
        ))
        conn.commit()
        application_id = cursor.lastrowid

        # Get job details for notification
        job_query = """
            SELECT title FROM job_submissions WHERE id = %s
        """
        cursor.execute(job_query, (job_id,))
        job_result = cursor.fetchone()
        job_title = job_result['title'] if job_result else 'Unknown Job'

        cursor.close()
        conn.close()

        # Send real-time notification to client using Socket.IO
        try:
            notification_data = {
                'type': 'new_application',
                'application_id': application_id,
                'job_id': job_id,
                'job_title': job_title,
                'genius_name': f"{first_name} {last_name}",
                'genius_id': session.get('user_id'),
                'message': f"{first_name} {last_name} applied for your job: {job_title}",
                'timestamp': datetime.now().strftime('%Y-%m-%dT%H:%M:%S.000Z')
            }

            # Emit to the specific client's room
            socketio.emit('new_job_application', notification_data, room=str(client_id))
            print(f"Real-time notification sent to client {client_id} for new application")

        except Exception as e:
            print(f"Error sending real-time notification: {e}")

        return jsonify(success=True, message="Application submitted successfully", application_id=application_id)

    except Exception as e:
        print(f"Error applying to job: {e}")
        return jsonify(success=False, error="An error occurred while submitting your application")

@app.route('/get_client_notifications')
@login_required
def get_client_notifications():
    """API endpoint to get notifications for client users about job applications"""
    # Check if user is a client
    if session.get('user_type') != 'client':
        return jsonify(success=False, error="Access denied. Only client users can access this endpoint.")

    try:
        conn = get_db_connection()
        cursor = conn.cursor(dictionary=True)

        # Check if applications table exists
        check_table_query = """
            SELECT COUNT(*)
            FROM information_schema.tables
            WHERE table_schema = 'giggenius'
            AND table_name = 'applications'
        """
        cursor.execute(check_table_query)
        table_exists = cursor.fetchone()['COUNT(*)'] > 0

        notifications = []
        if table_exists:
            # Get applications for jobs posted by this client
            query = """
                SELECT
                    a.id, a.genius_id, a.job_id, a.first_name, a.last_name,
                    a.status, a.created_at,
                    js.title as job_title
                FROM applications a
                JOIN job_submissions js ON a.job_id = js.id
                WHERE js.client_id = %s
                ORDER BY a.created_at DESC
                LIMIT 50
            """
            cursor.execute(query, (session.get('user_id'),))
            notifications = cursor.fetchall()

            # Format dates for JSON serialization
            for notification in notifications:
                if notification.get('created_at'):
                    notification['created_at'] = notification['created_at'].strftime('%Y-%m-%dT%H:%M:%S.000Z')

        cursor.close()
        conn.close()

        return jsonify(success=True, notifications=notifications)

    except Exception as e:
        print(f"Error fetching client notifications: {e}")
        return jsonify(success=False, error=str(e))

@app.route('/get_client_contracts')
@login_required
def get_client_contracts():
    """API endpoint to get contracts for client users"""
    # Check if user is a client
    if session.get('user_type') != 'client':
        return jsonify(success=False, error="Access denied. Only client users can access this endpoint.")

    try:
        conn = get_db_connection()
        cursor = conn.cursor(dictionary=True)

        # Check if applications table exists
        check_table_query = """
            SELECT COUNT(*)
            FROM information_schema.tables
            WHERE table_schema = 'giggenius'
            AND table_name = 'applications'
        """
        cursor.execute(check_table_query)
        table_exists = cursor.fetchone()['COUNT(*)'] > 0

        contracts = []
        if table_exists:
            # First check what columns exist in the applications table
            check_columns_query = """
                SELECT COLUMN_NAME
                FROM INFORMATION_SCHEMA.COLUMNS
                WHERE TABLE_SCHEMA = 'giggenius'
                AND TABLE_NAME = 'applications'
            """
            cursor.execute(check_columns_query)
            app_columns = [row['COLUMN_NAME'] for row in cursor.fetchall()]

            # Build query based on available columns
            base_columns = "a.id, a.genius_id, a.job_id, a.first_name, a.last_name, a.status, a.created_at"
            optional_columns = []

            if 'proposal_text' in app_columns:
                optional_columns.append("a.proposal_text")
            else:
                optional_columns.append("NULL as proposal_text")

            if 'budget_amount' in app_columns:
                optional_columns.append("a.budget_amount")
            else:
                optional_columns.append("NULL as budget_amount")

            # Get accepted applications (contracts) for jobs posted by this client
            query = f"""
                SELECT
                    {base_columns},
                    {', '.join(optional_columns)},
                    js.title as job_title, js.description as job_description,
                    js.budget_type, js.budget_amount as job_budget, js.duration,
                    js.category, js.specialty, js.project_size,
                    ag.first_name as genius_first_name, ag.last_name as genius_last_name,
                    ag.profile_title, ag.hourly_rate, ag.country as genius_country
                FROM applications a
                JOIN job_submissions js ON a.job_id = js.id
                LEFT JOIN approve_genius ag ON a.genius_id = ag.id
                WHERE js.client_id = %s AND a.status IN ('accept', 'active', 'completed')
                ORDER BY a.created_at DESC
            """
            cursor.execute(query, (session.get('user_id'),))
            applications = cursor.fetchall()

            # Transform accepted applications into contract format
            for app in applications:
                # Only process accepted applications as contracts
                if app['status'] == 'accept':
                    contract_status = 'active'
                elif app['status'] == 'completed':
                    contract_status = 'completed'
                else:
                    # Skip any other status (shouldn't happen due to query filter)
                    continue

                contract = {
                    'id': app['id'],
                    'contract_id': f"CT-{app['id']:04d}",
                    'job_id': app['job_id'],
                    'genius_id': app['genius_id'],
                    'genius_name': f"{app['genius_first_name'] or app['first_name']} {app['genius_last_name'] or app['last_name']}",
                    'job_title': app['job_title'],
                    'job_description': app['job_description'],
                    'status': contract_status,
                    'application_status': app['status'],  # Keep original status for reference
                    'budget': app['budget_amount'] or app['job_budget'],
                    'budget_type': app['budget_type'],
                    'duration': app['duration'],
                    'category': app['category'],
                    'specialty': app['specialty'],
                    'project_size': app['project_size'],
                    'created_at': app['created_at'],
                    'start_date': app['created_at'],
                    'end_date': None,  # Calculate based on duration if needed
                    'progress': 0 if app['status'] == 'pending' else (100 if app['status'] == 'completed' else 50),
                    'proposal_text': app['proposal_text'],
                    'genius_profile_title': app['profile_title'],
                    'genius_hourly_rate': app['hourly_rate'],
                    'genius_country': app['genius_country']
                }
                contracts.append(contract)

        # If no contracts found, add some sample contracts for demonstration
        if len(contracts) == 0:
            from datetime import datetime, timedelta
            sample_contracts = [
                {
                    'id': 1,
                    'contract_id': 'CT-0001',
                    'job_id': 1,
                    'genius_id': 1,
                    'genius_name': 'John Smith',
                    'job_title': 'Website Development Project',
                    'job_description': 'Build a modern responsive website with React and Node.js',
                    'status': 'active',
                    'budget': 5500,
                    'budget_type': 'fixed',
                    'duration': '3 months',
                    'category': 'Web Development',
                    'specialty': 'Full Stack Development',
                    'project_size': 'Medium',
                    'created_at': datetime.now() - timedelta(days=15),
                    'start_date': datetime.now() - timedelta(days=15),
                    'end_date': None,
                    'progress': 65,
                    'proposal_text': 'I can build your website using modern technologies...',
                    'genius_profile_title': 'Full Stack Developer',
                    'genius_hourly_rate': 45,
                    'genius_country': 'Philippines'
                },
                {
                    'id': 2,
                    'contract_id': 'CT-0002',
                    'job_id': 2,
                    'genius_id': 2,
                    'genius_name': 'Maria Garcia',
                    'job_title': 'Mobile App UI Design',
                    'job_description': 'Design a modern mobile app interface for iOS and Android',
                    'status': 'completed',
                    'budget': 3200,
                    'budget_type': 'fixed',
                    'duration': '6 weeks',
                    'category': 'Design',
                    'specialty': 'UI/UX Design',
                    'project_size': 'Small',
                    'created_at': datetime.now() - timedelta(days=45),
                    'start_date': datetime.now() - timedelta(days=45),
                    'end_date': datetime.now() - timedelta(days=5),
                    'progress': 100,
                    'proposal_text': 'I specialize in mobile app design with 5+ years experience...',
                    'genius_profile_title': 'UI/UX Designer',
                    'genius_hourly_rate': 35,
                    'genius_country': 'Philippines'
                },
                {
                    'id': 3,
                    'contract_id': 'CT-0003',
                    'job_id': 3,
                    'genius_id': 3,
                    'genius_name': 'Sarah Wilson',
                    'job_title': 'E-commerce Platform Development',
                    'job_description': 'Build a complete e-commerce platform with payment integration',
                    'status': 'active',
                    'budget': 8500,
                    'budget_type': 'fixed',
                    'duration': '4 months',
                    'category': 'Web Development',
                    'specialty': 'E-commerce Development',
                    'project_size': 'Large',
                    'created_at': datetime.now() - timedelta(days=30),
                    'start_date': datetime.now() - timedelta(days=30),
                    'end_date': None,
                    'progress': 35,
                    'proposal_text': 'I have extensive experience building e-commerce platforms...',
                    'genius_profile_title': 'E-commerce Developer',
                    'genius_hourly_rate': 55,
                    'genius_country': 'Philippines'
                }
            ]
            contracts.extend(sample_contracts)

            # Format dates for JSON serialization
            for contract in contracts:
                if contract.get('created_at'):
                    contract['created_at'] = contract['created_at'].strftime('%Y-%m-%dT%H:%M:%S.000Z')
                if contract.get('start_date'):
                    contract['start_date'] = contract['start_date'].strftime('%Y-%m-%dT%H:%M:%S.000Z')

        cursor.close()
        conn.close()

        return jsonify(success=True, contracts=contracts)

    except Exception as e:
        print(f"Error fetching client contracts: {e}")
        return jsonify(success=False, error=str(e))

@app.route('/handle-application/<int:application_id>/<string:action>', methods=['POST'])
@login_required
def handle_application(application_id, action):
    """Handle accepting or rejecting an application from notifications"""
    # Check if user is a client
    if session.get('user_type') != 'client':
        return jsonify(success=False, error="Access denied. Only client users can handle applications.")

    # Validate action
    if action not in ['accept', 'reject']:
        return jsonify(success=False, error="Invalid action. Must be 'accept' or 'reject'.")

    try:
        conn = get_db_connection()
        cursor = conn.cursor(dictionary=True)

        # Verify the application belongs to a job posted by this client
        verify_query = """
            SELECT a.id, a.genius_id, a.job_id, a.status, js.client_id, js.title as job_title,
                   ag.first_name, ag.last_name
            FROM applications a
            JOIN job_submissions js ON a.job_id = js.id
            LEFT JOIN approve_genius ag ON a.genius_id = ag.id
            WHERE a.id = %s AND js.client_id = %s
        """
        cursor.execute(verify_query, (application_id, session.get('user_id')))
        application = cursor.fetchone()

        if not application:
            return jsonify(success=False, error="Application not found or access denied.")

        if application['status'] != 'pending':
            return jsonify(success=False, error="Only pending applications can be accepted or rejected.")

        # Update application status
        update_query = """
            UPDATE applications
            SET status = %s, updated_at = NOW()
            WHERE id = %s
        """
        cursor.execute(update_query, (action, application_id))

        # If accepting, send automatic message to genius
        if action == 'accept':
            try:
                # Send automatic acceptance message
                message_text = f"Congratulations! Your application for '{application['job_title']}' has been accepted. The client will contact you soon with project details."

                insert_message_query = """
                    INSERT INTO messages (
                        sender_id, sender_type, receiver_id, receiver_type,
                        message_text, is_read, is_auto, related_to_job_id,
                        related_to_application_id, timestamp
                    ) VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, NOW())
                """
                cursor.execute(insert_message_query, (
                    session.get('user_id'),  # client_id as sender
                    'client',
                    application['genius_id'],  # genius_id as receiver
                    'genius',
                    message_text,
                    False,  # is_read
                    True,   # is_auto (automatic message)
                    application['job_id'],
                    application_id
                ))

                print(f"Automatic acceptance message sent from client {session.get('user_id')} to genius {application['genius_id']}")

            except Exception as e:
                print(f"Error sending automatic acceptance message: {e}")
                # Don't fail the whole operation if message sending fails

        conn.commit()
        cursor.close()
        conn.close()

        action_text = "accepted" if action == "accept" else "rejected"
        return jsonify(
            success=True,
            message=f"Application {action_text} successfully!",
            automatic_message_sent=(action == 'accept')
        )

    except Exception as e:
        print(f"Error handling application: {e}")
        return jsonify(success=False, error=str(e))



@app.route('/test_notification/<int:client_id>')
def test_notification(client_id):
    """Test endpoint to simulate a real-time notification"""
    try:
        # Send test notification
        notification_data = {
            'type': 'new_application',
            'application_id': 999,
            'job_id': 123,
            'job_title': 'Test Job - Web Developer',
            'genius_name': 'John Doe',
            'genius_id': 456,
            'message': 'John Doe applied for your job: Test Job - Web Developer',
            'timestamp': datetime.now().strftime('%Y-%m-%dT%H:%M:%S.000Z')
        }

        # Emit to the specific client's room
        socketio.emit('new_job_application', notification_data, room=str(client_id))
        print(f"Test notification sent to client {client_id}")

        return jsonify(success=True, message=f"Test notification sent to client {client_id}")

    except Exception as e:
        print(f"Error sending test notification: {e}")
        return jsonify(success=False, error=str(e))

@app.route('/withdraw_application/<int:application_id>', methods=['POST'])
@login_required
def withdraw_application(application_id):
    """API endpoint to withdraw a job application"""
    # Only geniuses can withdraw their applications
    if session.get('user_type') != 'genius':
        return jsonify(success=False, error="Only geniuses can withdraw applications")

    try:
        conn = get_db_connection()
        cursor = conn.cursor(dictionary=True)

        # Check if the application exists and belongs to the current user
        check_query = """
            SELECT id, status FROM applications
            WHERE id = %s AND genius_id = %s
        """
        cursor.execute(check_query, (application_id, session.get('user_id')))
        application = cursor.fetchone()

        if not application:
            cursor.close()
            conn.close()
            return jsonify(success=False, error="Application not found or you don't have permission to withdraw it")

        # Check if application can be withdrawn (only pending applications)
        if application['status'] not in ['pending']:
            cursor.close()
            conn.close()
            return jsonify(success=False, error="This application cannot be withdrawn as it has already been processed")

        # Delete the application
        delete_query = """
            DELETE FROM applications
            WHERE id = %s AND genius_id = %s
        """
        cursor.execute(delete_query, (application_id, session.get('user_id')))
        conn.commit()

        if cursor.rowcount > 0:
            cursor.close()
            conn.close()
            return jsonify(success=True, message="Application withdrawn successfully")
        else:
            cursor.close()
            conn.close()
            return jsonify(success=False, error="Failed to withdraw application")

    except Exception as e:
        print(f"Error withdrawing application: {e}")
        return jsonify(success=False, error="An error occurred while withdrawing your application")

@app.route('/check_application_status/<int:job_id>')
@login_required
def check_application_status(job_id):
    """API endpoint to check if the current user has applied to a specific job"""
    # Only geniuses can check application status
    if session.get('user_type') != 'genius':
        return jsonify(success=False, error="Access denied")

    try:
        conn = get_db_connection()
        cursor = conn.cursor(dictionary=True)

        # Check if applications table exists
        check_table_query = """
            SELECT COUNT(*)
            FROM information_schema.tables
            WHERE table_schema = 'giggenius'
            AND table_name = 'applications'
        """
        cursor.execute(check_table_query)
        table_exists = cursor.fetchone()['COUNT(*)'] > 0

        if not table_exists:
            cursor.close()
            conn.close()
            return jsonify(success=True, has_applied=False, status=None)

        # Check if user has applied to this job
        check_query = """
            SELECT id, status, created_at FROM applications
            WHERE genius_id = %s AND job_id = %s
        """
        cursor.execute(check_query, (session.get('user_id'), job_id))
        application = cursor.fetchone()

        cursor.close()
        conn.close()

        if application:
            return jsonify(
                success=True,
                has_applied=True,
                status=application['status'],
                application_id=application['id'],
                applied_date=application['created_at'].strftime('%Y-%m-%d') if application['created_at'] else None
            )
        else:
            return jsonify(success=True, has_applied=False, status=None)

    except Exception as e:
        print(f"Error checking application status: {e}")
        return jsonify(success=False, error="An error occurred while checking application status")

@app.route('/check_status_column')
@login_required
def check_status_column():
    """Debug route to check what status values exist in the database"""
    try:
        conn = get_db_connection()
        cursor = conn.cursor(dictionary=True)

        # Check distinct status values
        query = """
            SELECT DISTINCT status, COUNT(*) as count
            FROM job_submissions
            GROUP BY status
            ORDER BY count DESC
        """
        cursor.execute(query)
        status_results = cursor.fetchall()

        # Also get some sample records
        sample_query = """
            SELECT id, title, status, created_at
            FROM job_submissions
            ORDER BY created_at DESC
            LIMIT 10
        """
        cursor.execute(sample_query)
        sample_results = cursor.fetchall()

        cursor.close()
        conn.close()

        return f"""
        <h2>Status Column Analysis</h2>
        <h3>Distinct Status Values:</h3>
        <ul>
        {''.join([f"<li><strong>{r['status']}</strong>: {r['count']} records</li>" for r in status_results])}
        </ul>

        <h3>Sample Records (Latest 10):</h3>
        <table border="1" style="border-collapse: collapse;">
        <tr><th>ID</th><th>Title</th><th>Status</th><th>Created At</th></tr>
        {''.join([f"<tr><td>{r['id']}</td><td>{r['title']}</td><td><strong>{r['status']}</strong></td><td>{r['created_at']}</td></tr>" for r in sample_results])}
        </table>
        """

    except Exception as e:
        return f"Error: {str(e)}"

@app.route('/affiliate_program')
def affiliate_program():
    return render_template('f_affiliate_program.html')

@app.route('/news_and_events')
def news_and_events():
    # You can add logic here to fetch news and events from the database
    # For now, we'll just render the template with empty data
    news_events = []  # This would normally be populated from a database
    return render_template('f_news_and_events.html', news_events=news_events)

@app.route('/why_giggenius')
def why_giggenius():
    return render_template('why_giggenius.html')

# Placeholder routes for footer links
@app.route('/how_to_hire')
def how_to_hire():
    return render_template('f_how_to_hire.html')

@app.route('/accounting_services')
def accounting_services():
    return render_template('f_accounting_services.html')

@app.route('/marketplace')
def marketplace():
    return render_template('landing_page.html')

@app.route('/service_catalog')
def service_catalog():
    return render_template('landing_page.html')

@app.route('/payroll_services')
def payroll_services():
    return render_template('landing_page.html')

@app.route('/business_networking')
def business_networking():
    return render_template('landing_page.html')

@app.route('/events')
def events():
    return render_template('f_events.html')

@app.route('/ph_business_loan')
def ph_business_loan():
    return render_template('f_ph_business_loan.html')

@app.route('/how_it_works')
def how_it_works():
    return render_template('f_how_it_works.html')

@app.route('/why_cant_apply')
def why_cant_apply():
    return render_template('f_why_cant_apply.html')

@app.route('/direct_contracts')
def direct_contracts():
    return render_template('f_direct_contracts.html')

@app.route('/find_mentors')
def find_mentors():
    return render_template('f_find_mentors.html')

@app.route('/ph_health_insurance')
def ph_health_insurance():
    return render_template('f_ph_health_insurance.html')

@app.route('/ph_life_insurance')
def ph_life_insurance():
    return render_template('f_ph_life_insurance.html')

@app.route('/mentor_application')
def mentor_application():
    return render_template('f_mentor_application.html')

@app.route('/help_and_support')
def help_and_support():
    return render_template('f_help_and_support.html')

@app.route('/about_us')
def about_us():
    return render_template('f_about_us.html')

@app.route('/contact_us')
def contact_us():
    return render_template('f_contact_us.html')

@app.route('/charity_projects')
def charity_projects():
    return render_template('f_charity_projects.html')

@app.route('/terms_of_service')
def terms_of_service():
    return render_template('f_terms_of_service.html')

@app.route('/privacy_policy')
def privacy_policy():
    return render_template('f_privacy_policy.html')

@app.route('/user_agreement')
def user_agreement():
    return render_template('f_user_agreement.html')


# GENIUS REGISTRATION ROUTE
@app.route('/register_genius', methods=['GET', 'POST'])
def register_genius():
    if request.method == 'POST':
        try:
            conn = get_db_connection()
            if conn is None:
                return jsonify({
                    'success': False,
                    'error': 'Database connection failed'
                })

            cursor = conn.cursor()

            # Handle file uploads with better error checking and debugging
            profile_photo = None
            id_front = None
            id_back = None

            # Debug print
            print("Files received:", list(request.files.keys()))

            if 'profilePhoto' in request.files:
                file = request.files['profilePhoto']
                if file.filename != '':
                    profile_photo = file.read()
                    print("Profile photo size:", len(profile_photo))

            if 'idFront' in request.files:
                file = request.files['idFront']
                if file.filename != '':
                    id_front = file.read()
                    print("ID Front size:", len(id_front))

            if 'idBack' in request.files:
                file = request.files['idBack']
                if file.filename != '':
                    id_back = file.read()
                    print("ID Back size:", len(id_back))

            # Validate that ID photos are present
            if not id_front or not id_back:
                return jsonify({
                    'success': False,
                    'error': 'Both front and back ID photos are required'
                }), 400

            # Validate required fields
            required_fields = ['firstName', 'lastName', 'email', 'password', 'birthday',
                             'country', 'mobile', 'position', 'expertise', 'hourly_rate',
                             'availability', 'tax_id', 'introduction', 'professional_sum']

            missing_fields = [field for field in required_fields
                            if field not in request.form or not request.form[field].strip()]

            if missing_fields:
                return jsonify({
                    'success': False,
                    'error': f'Missing required fields: {", ".join(missing_fields)}'
                }), 400

            # Convert checkbox values to tinyint with default 0
            email_updates = 1 if request.form.get('email_updates') == '1' else 0
            terms_agreement = 1 if request.form.get('terms_agreement') == '1' else 0

            # SQL query
            sql = """
            INSERT INTO register_genius (
                profile_photo, first_name, last_name, email, password,
                birthday, country, mobile, position, expertise,
                hourly_rate, availability, tax_id, introduction, professional_sum,
                id_front, id_back, email_updates, terms_agreement
            ) VALUES (
                %s, %s, %s, %s, %s, %s, %s, %s, %s, %s,
                %s, %s, %s, %s, %s, %s, %s, %s, %s
            )
            """

            # Prepare data tuple
            data = (
                profile_photo,
                request.form['firstName'].strip(),
                request.form['lastName'].strip(),
                request.form['email'].strip(),
                request.form['password'],
                request.form['birthday'],
                request.form['country'],
                request.form['mobile'].strip(),
                request.form['position'],
                request.form['expertise'],
                float(request.form['hourly_rate']),
                request.form['availability'],
                request.form['tax_id'].strip(),
                request.form['introduction'].strip(),
                request.form['professional_sum'].strip(),
                id_front,
                id_back,
                email_updates,
                terms_agreement
            )

            # Debug print the data tuple (excluding binary data)
            print("Data to be inserted:", [
                "binary_data" if isinstance(x, bytes) else x
                for x in data
            ])

            cursor.execute(sql, data)
            conn.commit()

            return jsonify({
                'success': True,
                'message': 'Registration successful'
            })

        except mysql.connector.Error as err:
            print(f"Database Error: {err}")
            if conn:
                conn.rollback()
            return jsonify({
                'success': False,
                'error': f'Database error: {str(err)}'
            }), 500

        except Exception as e:
            print(f"Unexpected error: {str(e)}")
            if conn:
                conn.rollback()
            return jsonify({
                'success': False,
                'error': f'Unexpected error: {str(e)}'
            }), 500

        finally:
            if 'cursor' in locals():
                cursor.close()
            if conn:
                conn.close()

    return render_template('genius_registration.html')

# Add route alias for genius_registration to maintain backward compatibility
@app.route('/genius_registration', methods=['GET', 'POST'])
def genius_registration():
    """Alias for register_genius to maintain backward compatibility with templates"""
    return register_genius()

# Add route alias for client_registration to maintain backward compatibility
@app.route('/client_registration', methods=['GET', 'POST'])
def client_registration():
    """Alias for register_client to maintain backward compatibility with templates"""
    return register_client()

# CLIENT REGISTRATION ROUTE
@app.route('/register_client', methods=['GET', 'POST'])
def register_client():
    if request.method == 'POST':
        try:
            conn = get_db_connection()
            if conn is None:
                return jsonify({
                    'success': False,
                    'error': 'Database connection failed'
                })

            cursor = conn.cursor()

            # Handle file uploads
            profile_photo = None
            business_logo = None
            business_reg_doc = None

            print("Files received:", list(request.files.keys()))

            if 'profilePhoto' in request.files:
                file = request.files['profilePhoto']
                if file.filename != '':
                    profile_photo = file.read()
                    print("Profile photo size:", len(profile_photo))

            if 'businessLogo' in request.files:
                file = request.files['businessLogo']
                if file.filename != '':
                    business_logo = file.read()
                    print("Business logo size:", len(business_logo))

            if 'businessReg' in request.files:
                file = request.files['businessReg']
                if file.filename != '':
                    business_reg_doc = file.read()
                    print("Business registration doc size:", len(business_reg_doc))

            # Validate required fields
            required_fields = [
                'firstName', 'lastName', 'email', 'password', 'birthday',
                'country', 'mobile', 'position', 'businessName',
                'businessAddress', 'businessEmail', 'industry',
                'employeeCount'
            ]

            missing_fields = [field for field in required_fields if not request.form.get(field)]
            if missing_fields:
                return jsonify({
                    'success': False,
                    'error': f'Missing required fields: {", ".join(missing_fields)}'
                }), 400

            # Convert checkbox values to tinyint
            email_updates = 1 if request.form.get('email_updates') == '1' else 0
            terms_agreement = 1 if request.form.get('terms_agreement') == '1' else 0

            # SQL query
            sql = """
            INSERT INTO register_client (
                profile_photo, first_name, last_name, work_email, password,
                birthday, country, mobile, position, business_logo,
                business_name, business_address, business_email, industry,
                business_website, employee_count, introduction, business_registration_doc,
                email_updates, terms_agreement, status
            ) VALUES (
                %s, %s, %s, %s, %s, %s, %s, %s, %s, %s,
                %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s
            )
            """

            # Prepare data tuple
            data = (
                profile_photo,
                request.form['firstName'].strip(),
                request.form['lastName'].strip(),
                request.form['email'].strip(),
                request.form['password'],
                request.form['birthday'],
                request.form['country'],
                request.form['mobile'].strip(),
                request.form['position'].strip(),
                business_logo,
                request.form['businessName'].strip(),
                request.form['businessAddress'].strip(),
                request.form['businessEmail'].strip(),
                request.form['industry'],
                request.form.get('businessWebsite', '').strip(),
                request.form['employeeCount'],
                request.form['introduction'].strip(),
                business_reg_doc,
                email_updates,
                terms_agreement,
                'pending'
            )

            # Debug print the data tuple (excluding binary data)
            print("Data to be inserted:", [
                "binary_data" if isinstance(x, bytes) else x
                for x in data
            ])

            cursor.execute(sql, data)
            conn.commit()

            return jsonify({
                'success': True,
                'message': 'Registration successful'
            })

        except mysql.connector.Error as err:
            print(f"Database Error: {err}")
            if conn:
                conn.rollback()
            return jsonify({
                'success': False,
                'error': f'Database error: {str(err)}'
            }), 500

        except Exception as e:
            print(f"Unexpected error: {str(e)}")
            if conn:
                conn.rollback()
            return jsonify({
                'success': False,
                'error': f'Unexpected error: {str(e)}'
            }), 500

        finally:
            if 'cursor' in locals():
                cursor.close()
            if conn:
                conn.close()

    return render_template('client_registration.html')

# Affiliate program routes
@app.route('/affiliate_register', methods=['POST'])
def affiliate_register():
    """Handle affiliate registration"""
    try:
        # Get form data
        first_name = request.form.get('firstName')
        last_name = request.form.get('lastName')
        phone = request.form.get('phone')
        email = request.form.get('email')
        password = request.form.get('password')

        # Validate required fields
        if not all([first_name, last_name, phone, email, password]):
            return jsonify(success=False, message="All fields are required")

        # TODO: Add actual database insertion for affiliate registration
        # For now, just return success
        return jsonify(success=True, message="Registration successful! We'll review your application and get back to you within 24 hours.")

    except Exception as e:
        print(f"Error in affiliate registration: {e}")
        return jsonify(success=False, message=f"An error occurred: {str(e)}")

@app.route('/add_portfolio_project', methods=['POST'])
@login_required
def add_portfolio_project():
    """Handle adding a new portfolio project"""
    print(f"Portfolio project add request received from user: {session.get('user_id')}, type: {session.get('user_type')}")

    # Only geniuses can add portfolio projects
    if session.get('user_type') != 'genius':
        print("Access denied: User is not a genius")
        return jsonify(success=False, error="Only geniuses can add portfolio projects")

    try:
        # Get form data
        project_title = request.form.get('project_title', '').strip()
        project_role = request.form.get('project_role', '').strip()
        project_description = request.form.get('project_description', '').strip()
        skills_and_deliverables = request.form.get('skills_and_deliverables', '').strip()
        related_giggenius_job = request.form.get('related_giggenius_job', '').strip()
        project_image = request.files.get('project_image')
        status = request.form.get('status', 'draft')  # 'draft' or 'published'

        print(f"Received form data:")
        print(f"  project_title: '{project_title}'")
        print(f"  project_role: '{project_role}'")
        print(f"  project_description: '{project_description}'")
        print(f"  skills_and_deliverables: '{skills_and_deliverables}'")
        print(f"  related_giggenius_job: '{related_giggenius_job}'")
        print(f"  status: '{status}'")
        print(f"  project_image: {project_image}")

        # Validate required fields
        if not project_title:
            print("Validation failed: Project title is required")
            return jsonify(success=False, error="Project title is required")

        # Make project description optional for now to test
        # if not project_description:
        #     print("Validation failed: Project description is required")
        #     return jsonify(success=False, error="Project description is required")

        # Get database connection
        print("Getting database connection...")
        conn = get_db_connection()
        cursor = conn.cursor(dictionary=True, buffered=True)
        print("Database connection established")

        # Check if portfolio_projects table exists, create if not
        print("Checking if portfolio_projects table exists...")
        check_table_query = """
            SELECT COUNT(*)
            FROM information_schema.tables
            WHERE table_schema = 'giggenius'
            AND table_name = 'portfolio_projects'
        """
        cursor.execute(check_table_query)
        table_exists = cursor.fetchone()['COUNT(*)'] > 0
        print(f"Table exists: {table_exists}")

        if not table_exists:
            # Create portfolio_projects table
            print("Creating portfolio_projects table...")
            create_table_query = """
                CREATE TABLE portfolio_projects (
                    id INT AUTO_INCREMENT PRIMARY KEY,
                    genius_id INT NOT NULL,
                    project_title VARCHAR(255) NOT NULL,
                    project_role VARCHAR(255) NULL,
                    project_description TEXT NULL,
                    skills_and_deliverables TEXT NULL,
                    related_giggenius_job VARCHAR(255) NULL,
                    project_image LONGBLOB NULL,
                    project_image_filename VARCHAR(255) NULL,
                    project_image_mimetype VARCHAR(100) NULL,
                    status VARCHAR(50) DEFAULT 'draft',
                    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                    INDEX (genius_id),
                    INDEX (status)
                )
            """
            cursor.execute(create_table_query)
            print("Table created successfully")
            conn.commit()
            print("Created portfolio_projects table")

        # Handle image upload
        image_data = None
        image_filename = None
        image_mimetype = None

        if project_image and project_image.filename:
            # Validate file type
            allowed_extensions = {'png', 'jpg', 'jpeg', 'gif', 'webp'}
            file_extension = project_image.filename.rsplit('.', 1)[1].lower() if '.' in project_image.filename else ''

            if file_extension not in allowed_extensions:
                return jsonify(success=False, error="Invalid file type. Please upload PNG, JPG, JPEG, GIF, or WEBP files only.")

            # Read image data
            image_data = project_image.read()
            image_filename = project_image.filename
            image_mimetype = project_image.mimetype

        # Insert portfolio project
        print("Inserting portfolio project...")
        insert_query = """
            INSERT INTO portfolio_projects (
                genius_id, project_title, project_role, project_description,
                skills_and_deliverables, related_giggenius_job, project_image,
                project_image_filename, project_image_mimetype, status
            ) VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
        """

        insert_values = (
            session.get('user_id'),
            project_title,
            project_role,
            project_description,
            skills_and_deliverables,
            related_giggenius_job,
            image_data,
            image_filename,
            image_mimetype,
            status
        )
        print(f"Insert values: genius_id={session.get('user_id')}, title='{project_title}', status='{status}'")

        cursor.execute(insert_query, insert_values)

        conn.commit()
        project_id = cursor.lastrowid
        print(f"Portfolio project inserted successfully with ID: {project_id}")

        # Update approve_genius table with project_title
        print(f"Updating approve_genius table with project_title: '{project_title}'")
        update_genius_query = """
            UPDATE approve_genius
            SET project_title = %s
            WHERE id = %s
        """
        cursor.execute(update_genius_query, (project_title, session.get('user_id')))
        conn.commit()
        print("Project title updated in approve_genius table successfully")

        cursor.close()
        conn.close()

        return jsonify(success=True, message="Portfolio project added successfully!", project_id=project_id)

    except Exception as e:
        print(f"Error adding portfolio project: {e}")
        return jsonify(success=False, error="An error occurred while saving your portfolio project")

@app.route('/save_portfolio_title', methods=['POST'])
@login_required
def save_portfolio_title():
    """Handle saving portfolio project title to approve_genius table"""
    print(f"Portfolio title save request received from user: {session.get('user_id')}, type: {session.get('user_type')}")

    # Only geniuses can save portfolio titles
    if session.get('user_type') != 'genius':
        print("Access denied: User is not a genius")
        return jsonify(success=False, error="Only geniuses can save portfolio titles")

    try:
        # Get form data
        data = request.get_json()
        if not data:
            print("No JSON data received")
            return jsonify(success=False, error="No data received")

        project_title = data.get('project_title', '').strip()
        project_role = data.get('project_role', '').strip()
        project_description = data.get('project_description', '').strip()
        project_content = data.get('project_content', '').strip()
        skills_and_deliverables = data.get('skills_and_deliverables', '').strip()
        related_giggenius_job = data.get('related_giggenius_job', '').strip()
        action = data.get('action', 'draft')  # 'draft' or 'published'

        print(f"Form data received: title='{project_title}', role='{project_role}', description='{project_description}', content='{project_content}', skills='{skills_and_deliverables}', related_job='{related_giggenius_job}', action='{action}'")

        # Validate required fields
        if not project_title:
            print("Validation failed: Project title is required")
            return jsonify(success=False, error="Project title is required")

        conn = get_db_connection()
        cursor = conn.cursor(dictionary=True, buffered=True)

        # Check if approve_genius table has project_title column
        check_title_column_query = """
            SELECT COUNT(*)
            FROM information_schema.columns
            WHERE table_schema = 'giggenius'
            AND table_name = 'approve_genius'
            AND column_name = 'project_title'
        """
        cursor.execute(check_title_column_query)
        title_column_exists = cursor.fetchone()['COUNT(*)'] > 0
        print(f"Project title column exists in approve_genius: {title_column_exists}")

        if not title_column_exists:
            # Add project_title column to approve_genius table
            print("Adding project_title column to approve_genius table...")
            alter_table_query = """
                ALTER TABLE approve_genius
                ADD COLUMN project_title VARCHAR(255) NULL
            """
            cursor.execute(alter_table_query)
            conn.commit()
            print("Project title column added successfully")

        # Check if approve_genius table has project_role column
        check_role_column_query = """
            SELECT COUNT(*)
            FROM information_schema.columns
            WHERE table_schema = 'giggenius'
            AND table_name = 'approve_genius'
            AND column_name = 'project_role'
        """
        cursor.execute(check_role_column_query)
        role_column_exists = cursor.fetchone()['COUNT(*)'] > 0
        print(f"Project role column exists in approve_genius: {role_column_exists}")

        if not role_column_exists:
            # Add project_role column to approve_genius table
            print("Adding project_role column to approve_genius table...")
            alter_role_table_query = """
                ALTER TABLE approve_genius
                ADD COLUMN project_role VARCHAR(255) NULL
            """
            cursor.execute(alter_role_table_query)
            conn.commit()
            print("Project role column added successfully")

        # Check if approve_genius table has project_description column
        check_desc_column_query = """
            SELECT COUNT(*)
            FROM information_schema.columns
            WHERE table_schema = 'giggenius'
            AND table_name = 'approve_genius'
            AND column_name = 'project_description'
        """
        cursor.execute(check_desc_column_query)
        desc_column_exists = cursor.fetchone()['COUNT(*)'] > 0
        print(f"Project description column exists in approve_genius: {desc_column_exists}")

        if not desc_column_exists:
            # Add project_description column to approve_genius table
            print("Adding project_description column to approve_genius table...")
            alter_desc_table_query = """
                ALTER TABLE approve_genius
                ADD COLUMN project_description LONGTEXT NULL
            """
            cursor.execute(alter_desc_table_query)
            conn.commit()
            print("Project description column added successfully")
        else:
            # Column exists, but we need to ensure it's LONGTEXT to handle large descriptions
            print("Project description column exists, checking if it needs to be modified to LONGTEXT...")
            try:
                # Get current column definition
                cursor.execute("""
                    SELECT DATA_TYPE, CHARACTER_MAXIMUM_LENGTH
                    FROM information_schema.columns
                    WHERE table_schema = 'giggenius'
                    AND table_name = 'approve_genius'
                    AND column_name = 'project_description'
                """)
                column_info = cursor.fetchone()

                if column_info:
                    data_type = column_info['DATA_TYPE'].upper()
                    max_length = column_info['CHARACTER_MAXIMUM_LENGTH']
                    print(f"Current project_description column: {data_type}, max_length: {max_length}")

                    # If it's not LONGTEXT, modify it
                    if data_type != 'LONGTEXT':
                        print("Modifying project_description column to LONGTEXT...")
                        modify_desc_query = """
                            ALTER TABLE approve_genius
                            MODIFY COLUMN project_description LONGTEXT NULL
                        """
                        cursor.execute(modify_desc_query)
                        conn.commit()
                        print("Project description column modified to LONGTEXT successfully")
                    else:
                        print("Project description column is already LONGTEXT")

            except Exception as modify_error:
                print(f"Error checking/modifying project_description column: {modify_error}")
                # Continue anyway, the original error will be more informative

        # Check if skills_and_deliverables column exists
        cursor.execute("SHOW COLUMNS FROM approve_genius LIKE 'skills_and_deliverables'")
        skills_column_exists = cursor.fetchone() is not None
        print(f"Skills and deliverables column exists: {skills_column_exists}")

        if not skills_column_exists:
            # Add skills_and_deliverables column to approve_genius table
            print("Adding skills_and_deliverables column to approve_genius table...")
            alter_skills_table_query = """
                ALTER TABLE approve_genius
                ADD COLUMN skills_and_deliverables TEXT NULL
            """
            cursor.execute(alter_skills_table_query)
            conn.commit()
            print("Skills and deliverables column added successfully")
        else:
            # Column exists, ensure it's TEXT to handle large content
            print("Skills and deliverables column exists, checking if it needs to be modified to TEXT...")
            try:
                cursor.execute("""
                    SELECT DATA_TYPE, CHARACTER_MAXIMUM_LENGTH
                    FROM information_schema.columns
                    WHERE table_schema = 'giggenius'
                    AND table_name = 'approve_genius'
                    AND column_name = 'skills_and_deliverables'
                """)
                column_info = cursor.fetchone()

                if column_info:
                    data_type = column_info['DATA_TYPE'].upper()
                    max_length = column_info['CHARACTER_MAXIMUM_LENGTH']
                    print(f"Current skills_and_deliverables column: {data_type}, max_length: {max_length}")

                    # If it's VARCHAR with limited length, modify it to TEXT
                    if data_type == 'VARCHAR' and max_length and max_length < 65535:
                        print("Modifying skills_and_deliverables column to TEXT...")
                        modify_skills_query = """
                            ALTER TABLE approve_genius
                            MODIFY COLUMN skills_and_deliverables TEXT NULL
                        """
                        cursor.execute(modify_skills_query)
                        conn.commit()
                        print("Skills and deliverables column modified to TEXT successfully")
                    else:
                        print("Skills and deliverables column is already appropriate size")

            except Exception as modify_error:
                print(f"Error checking/modifying skills_and_deliverables column: {modify_error}")

        # Check if related_giggenius_job column exists
        cursor.execute("SHOW COLUMNS FROM approve_genius LIKE 'related_giggenius_job'")
        related_job_column_exists = cursor.fetchone() is not None
        print(f"Related GigGenius job column exists: {related_job_column_exists}")

        if not related_job_column_exists:
            # Add related_giggenius_job column to approve_genius table
            print("Adding related_giggenius_job column to approve_genius table...")
            alter_related_job_table_query = """
                ALTER TABLE approve_genius
                ADD COLUMN related_giggenius_job TEXT NULL
            """
            cursor.execute(alter_related_job_table_query)
            conn.commit()
            print("Related GigGenius job column added successfully")
        else:
            # Column exists, ensure it's TEXT to handle large content
            print("Related GigGenius job column exists, checking if it needs to be modified to TEXT...")
            try:
                cursor.execute("""
                    SELECT DATA_TYPE, CHARACTER_MAXIMUM_LENGTH
                    FROM information_schema.columns
                    WHERE table_schema = 'giggenius'
                    AND table_name = 'approve_genius'
                    AND column_name = 'related_giggenius_job'
                """)
                column_info = cursor.fetchone()

                if column_info:
                    data_type = column_info['DATA_TYPE'].upper()
                    max_length = column_info['CHARACTER_MAXIMUM_LENGTH']
                    print(f"Current related_giggenius_job column: {data_type}, max_length: {max_length}")

                    # If it's VARCHAR with limited length, modify it to TEXT
                    if data_type == 'VARCHAR' and max_length and max_length < 65535:
                        print("Modifying related_giggenius_job column to TEXT...")
                        modify_related_job_query = """
                            ALTER TABLE approve_genius
                            MODIFY COLUMN related_giggenius_job TEXT NULL
                        """
                        cursor.execute(modify_related_job_query)
                        conn.commit()
                        print("Related GigGenius job column modified to TEXT successfully")
                    else:
                        print("Related GigGenius job column is already appropriate size")

            except Exception as modify_error:
                print(f"Error checking/modifying related_giggenius_job column: {modify_error}")

        # Check if project_content column exists
        cursor.execute("SHOW COLUMNS FROM approve_genius LIKE 'project_content'")
        project_content_column_exists = cursor.fetchone() is not None
        print(f"Project content column exists: {project_content_column_exists}")

        if not project_content_column_exists:
            # Add project_content column to approve_genius table
            print("Adding project_content column to approve_genius table...")
            alter_project_content_table_query = """
                ALTER TABLE approve_genius
                ADD COLUMN project_content TEXT NULL
            """
            cursor.execute(alter_project_content_table_query)
            conn.commit()
            print("Project content column added successfully")
        else:
            # Column exists, ensure it's TEXT to handle large content
            print("Project content column exists, checking if it needs to be modified to TEXT...")
            try:
                cursor.execute("""
                    SELECT DATA_TYPE, CHARACTER_MAXIMUM_LENGTH
                    FROM information_schema.columns
                    WHERE table_schema = 'giggenius'
                    AND table_name = 'approve_genius'
                    AND column_name = 'project_content'
                """)
                column_info = cursor.fetchone()

                if column_info:
                    data_type = column_info['DATA_TYPE'].upper()
                    max_length = column_info['CHARACTER_MAXIMUM_LENGTH']
                    print(f"Current project_content column: {data_type}, max_length: {max_length}")

                    # If it's VARCHAR with limited length, modify it to TEXT
                    if data_type == 'VARCHAR' and max_length and max_length < 65535:
                        print("Modifying project_content column to TEXT...")
                        modify_project_content_query = """
                            ALTER TABLE approve_genius
                            MODIFY COLUMN project_content TEXT NULL
                        """
                        cursor.execute(modify_project_content_query)
                        conn.commit()
                        print("Project content column modified to TEXT successfully")
                    else:
                        print("Project content column is already appropriate size")

            except Exception as modify_error:
                print(f"Error checking/modifying project_content column: {modify_error}")

        # Check if portfolio_projects table exists, create if not
        check_table_query = """
            SELECT COUNT(*)
            FROM information_schema.tables
            WHERE table_schema = 'giggenius'
            AND table_name = 'portfolio_projects'
        """
        cursor.execute(check_table_query)
        table_exists = cursor.fetchone()['COUNT(*)'] > 0
        print(f"Portfolio projects table exists: {table_exists}")

        if not table_exists:
            # Create portfolio_projects table
            print("Creating portfolio_projects table...")
            create_table_query = """
                CREATE TABLE portfolio_projects (
                    id INT AUTO_INCREMENT PRIMARY KEY,
                    genius_id INT NOT NULL,
                    project_title VARCHAR(255) NOT NULL,
                    project_role VARCHAR(255) NULL,
                    project_description TEXT NULL,
                    project_content TEXT NULL,
                    skills_and_deliverables TEXT NULL,
                    related_giggenius_job VARCHAR(255) NULL,
                    project_image LONGBLOB NULL,
                    project_image_filename VARCHAR(255) NULL,
                    project_image_mimetype VARCHAR(100) NULL,
                    status VARCHAR(50) DEFAULT 'draft',
                    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                    INDEX (genius_id),
                    INDEX (status)
                )
            """
            cursor.execute(create_table_query)
            conn.commit()
            print("Portfolio projects table created successfully")
        else:
            # Check if status column exists, add if not
            print("Checking if status column exists...")
            check_status_column = """
                SELECT COUNT(*)
                FROM information_schema.columns
                WHERE table_schema = 'giggenius'
                AND table_name = 'portfolio_projects'
                AND column_name = 'status'
            """
            cursor.execute(check_status_column)
            status_column_exists = cursor.fetchone()['COUNT(*)'] > 0
            print(f"Status column exists: {status_column_exists}")

            if not status_column_exists:
                print("Adding status column to portfolio_projects table...")
                add_status_column = """
                    ALTER TABLE portfolio_projects
                    ADD COLUMN status VARCHAR(50) DEFAULT 'draft'
                """
                cursor.execute(add_status_column)
                conn.commit()
                print("Status column added successfully")

        # Insert portfolio project entry
        # Convert action to proper status
        status = 'published' if action == 'published' else 'draft'
        print(f"Inserting portfolio project with title: '{project_title}' and status: '{status}'")
        insert_query = """
            INSERT INTO portfolio_projects (
                genius_id, project_title, status
            ) VALUES (%s, %s, %s)
        """
        cursor.execute(insert_query, (session.get('user_id'), project_title, status))
        conn.commit()
        project_id = cursor.lastrowid
        print(f"Portfolio project inserted successfully with ID: {project_id}")

        # Update approve_genius table with all project fields
        print(f"Updating approve_genius table - Title: '{project_title}', Role: '{project_role}', Description: '{project_description}', Content: '{project_content}', Skills: '{skills_and_deliverables}', Related Job: '{related_giggenius_job}'")
        update_genius_query = """
            UPDATE approve_genius
            SET project_title = %s, project_role = %s, project_description = %s, project_content = %s, skills_and_deliverables = %s, related_giggenius_job = %s
            WHERE id = %s
        """
        cursor.execute(update_genius_query, (project_title, project_role, project_description, project_content, skills_and_deliverables, related_giggenius_job, session.get('user_id')))
        conn.commit()
        print("All project fields updated in approve_genius table successfully")

        cursor.close()
        conn.close()

        # Return appropriate response based on action
        if action == 'published':
            return jsonify(success=True, message="Project saved and published!", redirect="published")
        else:
            return jsonify(success=True, message="Project saved as draft!", redirect="drafts")

    except Exception as e:
        print(f"Error saving portfolio title: {e}")
        import traceback
        traceback.print_exc()
        return jsonify(success=False, error=f"An error occurred while saving your project title: {str(e)}")

@app.route('/get_portfolio_projects')
@login_required
def get_portfolio_projects():
    """Get portfolio projects for the current genius"""
    # Only geniuses can access their portfolio projects
    if session.get('user_type') != 'genius':
        return jsonify(success=False, error="Only geniuses can access portfolio projects")

    try:
        conn = get_db_connection()
        cursor = conn.cursor(dictionary=True, buffered=True)

        # Get portfolio projects for the current genius
        query = """
            SELECT id, project_title, project_role, project_description, project_content,
                   skills_and_deliverables, related_giggenius_job, status,
                   project_image_filename, project_image_mimetype,
                   created_at, updated_at
            FROM portfolio_projects
            WHERE genius_id = %s
            ORDER BY created_at DESC
        """

        cursor.execute(query, (session.get('user_id'),))
        projects = cursor.fetchall()

        print(f"Found {len(projects)} portfolio projects for user {session.get('user_id')}")
        for project in projects:
            print(f"Project: {project['project_title']} - Status: {project['status']}")

        # Convert datetime objects to strings for JSON serialization
        for project in projects:
            if project['created_at']:
                project['created_at'] = project['created_at'].strftime('%Y-%m-%d %H:%M:%S')
            if project['updated_at']:
                project['updated_at'] = project['updated_at'].strftime('%Y-%m-%d %H:%M:%S')

        cursor.close()
        conn.close()

        return jsonify(success=True, projects=projects)

    except Exception as e:
        print(f"Error getting portfolio projects: {e}")
        return jsonify(success=False, error="An error occurred while loading portfolio projects")

@app.route('/api/portfolio-image/<int:project_id>')
def api_portfolio_image(project_id):
    """API endpoint to get a portfolio project image"""
    try:
        conn = get_db_connection()
        cursor = conn.cursor(dictionary=True)

        query = "SELECT project_image, project_image_mimetype FROM portfolio_projects WHERE id = %s"
        cursor.execute(query, (project_id,))
        result = cursor.fetchone()

        cursor.close()
        conn.close()

        if result and result['project_image']:
            return Response(
                result['project_image'],
                mimetype=result['project_image_mimetype'] or 'image/jpeg',
                headers={'Cache-Control': 'public, max-age=3600'}
            )
        else:
            # Return a default placeholder image
            return redirect('https://via.placeholder.com/400x300?text=No+Image')

    except Exception as e:
        print(f"Error serving portfolio image: {e}")
        return redirect('https://via.placeholder.com/400x300?text=Error')

@app.route('/affiliate_login', methods=['POST'])
def affiliate_login():
    """Handle affiliate login"""
    try:
        # Get form data
        email = request.form.get('email')
        password = request.form.get('password')

        # Validate required fields
        if not all([email, password]):
            return jsonify(success=False, message="Email and password are required")

        # TODO: Add actual affiliate authentication
        # For now, just return success with a redirect
        return jsonify(success=True, redirect=url_for('affiliate_dashboard'))

    except Exception as e:
        print(f"Error in affiliate login: {e}")
        return jsonify(success=False, message=f"An error occurred: {str(e)}")

@app.route('/affiliate_dashboard')
def affiliate_dashboard():
    """Affiliate dashboard page"""
    # TODO: Add authentication check for affiliates
    return render_template('affiliate_dashboard.html')

@app.route('/verify_admin', methods=['POST'])
def verify_admin():
    """Verify admin security code"""
    try:
        # Get form data
        email = request.form.get('email')
        code = request.form.get('code')

        # Validate required fields
        if not all([email, code]):
            return jsonify(success=False, error="Email and security code are required")

        # TODO: Add actual verification logic
        # For now, just return success with a redirect to admin page
        # In a real implementation, you would verify the code against what was sent

        # For testing purposes, accept any code
        return jsonify(success=True, redirect=url_for('admin_page'))

    except Exception as e:
        print(f"Error in admin verification: {e}")
        return jsonify(success=False, error=f"An error occurred: {str(e)}")

@app.route('/check_email', methods=['POST'])
def check_email():
    """Check if an email is already registered"""
    try:
        # Get JSON data
        data = request.get_json()
        email = data.get('email')
        email_type = data.get('type')  # 'client' for business email, otherwise personal email

        if not email:
            return jsonify(success=False, error="Email is required"), 400

        # Connect to database
        conn = get_db_connection()
        cursor = conn.cursor(dictionary=True)

        exists = False

        if email_type == 'client':
            # For business email, only check business email fields
            # Check if business email exists in register_client table
            query = "SELECT COUNT(*) as count FROM register_client WHERE business_email = %s"
            cursor.execute(query, (email,))
            client_business_result = cursor.fetchone()

            # Check if business email exists in approve_client table
            query = "SELECT COUNT(*) as count FROM approve_client WHERE business_email = %s"
            cursor.execute(query, (email,))
            approve_client_business_result = cursor.fetchone()

            # Check if business email exists
            exists = (client_business_result['count'] > 0 or
                     approve_client_business_result['count'] > 0)
        else:
            # For personal email, check all email fields
            # Check if email exists in register_genius table
            query = "SELECT COUNT(*) as count FROM register_genius WHERE email = %s"
            cursor.execute(query, (email,))
            genius_result = cursor.fetchone()

            # Check if email exists in approve_genius table
            query = "SELECT COUNT(*) as count FROM approve_genius WHERE email = %s"
            cursor.execute(query, (email,))
            approve_genius_result = cursor.fetchone()

            # Check if email exists in register_client table as work_email
            query = "SELECT COUNT(*) as count FROM register_client WHERE work_email = %s"
            cursor.execute(query, (email,))
            client_result = cursor.fetchone()

            # Check if email exists in approve_client table as work_email
            query = "SELECT COUNT(*) as count FROM approve_client WHERE work_email = %s"
            cursor.execute(query, (email,))
            approve_client_result = cursor.fetchone()

            # Check if email exists in any table
            exists = (genius_result['count'] > 0 or
                     approve_genius_result['count'] > 0 or
                     client_result['count'] > 0 or
                     approve_client_result['count'] > 0)

        cursor.close()
        conn.close()

        return jsonify(success=True, exists=exists)

    except Exception as e:
        print(f"Error checking email: {e}")
        return jsonify(success=False, error=f"An error occurred: {str(e)}"), 500

@app.route('/verify_email', methods=['POST'])
def verify_email():
    """Verify email security code for registration"""
    try:
        # Get form data
        email = request.form.get('email')
        code = request.form.get('code')

        # Validate required fields
        if not all([email, code]):
            return jsonify(success=False, error="Email and security code are required")

        # TODO: Add actual verification logic
        # For now, just return success
        # In a real implementation, you would verify the code against what was sent

        # For testing purposes, accept any code
        return jsonify(success=True)

    except Exception as e:
        print(f"Error in email verification: {e}")
        return jsonify(success=False, error=f"An error occurred: {str(e)}")



@app.route('/socket-health')
def socket_health():
    """Health check endpoint for Socket.IO"""
    # Get active Socket.IO sessions
    active_sessions = len(socketio.server.manager.rooms) if hasattr(socketio, 'server') else 0

    return jsonify({
        'success': True,
        'status': 'healthy',
        'timestamp': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
        'active_sessions': active_sessions,
        'server_info': {
            'async_mode': socketio.async_mode,
            'cors_allowed_origins': socketio.cors_allowed_origins,
            'manage_session': socketio.manage_session
        }
    })

@app.route('/api/files/<int:message_id>')
@login_required
def get_file(message_id):
    """Retrieve a file from a message"""
    try:
        conn = get_db_connection()
        cursor = conn.cursor(dictionary=True)

        # Get the message with file data
        query = """
            SELECT
                id, sender_id, receiver_id, file_data,
                file_name, file_mime_type
            FROM messages
            WHERE id = %s AND message_type = 'file'
        """
        cursor.execute(query, (message_id,))
        message = cursor.fetchone()

        if not message:
            cursor.close()
            conn.close()
            return jsonify(success=False, error="File not found"), 404

        # Check if user has access to this file (is sender or receiver)
        user_id = session.get('user_id')
        if message['sender_id'] != user_id and message['receiver_id'] != user_id:
            cursor.close()
            conn.close()
            return jsonify(success=False, error="Access denied"), 403

        # Get file data
        file_data = message['file_data']
        file_name = message['file_name']
        file_mime_type = message['file_mime_type']

        cursor.close()
        conn.close()

        # Return file as attachment
        return send_file(
            io.BytesIO(file_data),
            mimetype=file_mime_type,
            as_attachment=True,
            download_name=file_name
        )

    except Exception as e:
        print(f"Error retrieving file: {e}")
        return jsonify(success=False, error="Error retrieving file"), 500

@socketio.on('send_file_message')
def handle_file_message(data):
    """Socket.IO event handler for file messages"""
    sender_id = session.get('user_id')
    receiver_id = data.get('receiver_id')
    file_data_b64 = data.get('file_data')
    file_name = data.get('file_name')
    file_mime_type = data.get('file_mime_type')
    related_to_job_id = data.get('related_to_job_id')
    related_to_application_id = data.get('related_to_application_id')

    if not sender_id or not receiver_id or not file_data_b64 or not file_name:
        emit('error', {'msg': 'Invalid file data'})
        return

    try:
        # Decode base64 file data
        file_data = base64.b64decode(file_data_b64)

        conn = get_db_connection()
        cursor = conn.cursor(dictionary=True)

        # Determine user types
        sender_type = 'client' if session.get('user_type') == 'client' else 'genius'
        receiver_type = 'client' if sender_type == 'genius' else 'genius'

        # Insert file message into database
        insert_query = """
            INSERT INTO messages (
                sender_id, sender_type, receiver_id, receiver_type, message_text,
                is_read, is_auto, related_to_job_id, related_to_application_id,
                message_type, timestamp, status, is_deleted, deleted_by_sender,
                deleted_by_receiver, file_data, file_name, file_mime_type
            ) VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, NOW(), %s, %s, %s, %s, %s, %s, %s)
        """

        # Determine message text based on file type
        message_text = "Sent a photo" if file_mime_type and file_mime_type.startswith('image/') else f"Sent a file: {file_name}"

        cursor.execute(
            insert_query,
            (
                sender_id,
                sender_type,
                receiver_id,
                receiver_type,
                message_text,  # Message text based on file type
                False,  # is_read
                False,  # is_auto
                related_to_job_id,
                related_to_application_id,
                'file',  # message_type
                'sending',  # status - use 'sending' to match client-side
                False,   # is_deleted
                False,   # deleted_by_sender
                False,   # deleted_by_receiver
                file_data,
                file_name,
                file_mime_type
            )
        )
        conn.commit()
        msg_id = cursor.lastrowid

        # Get the message with formatted timestamp (without file_data to reduce payload size)
        get_message_query = """
            SELECT
                id, sender_id, sender_type, receiver_id, receiver_type,
                message_text as message, is_read, is_auto,
                related_to_job_id, related_to_application_id,
                message_type, timestamp, status, is_deleted,
                deleted_by_sender, deleted_by_receiver,
                reply_to_id, replied_message_text,
                file_name, file_mime_type
            FROM messages
            WHERE id = %s
        """
        cursor.execute(get_message_query, (msg_id,))
        msg = cursor.fetchone()

        cursor.close()
        conn.close()

        # Format dates for JSON serialization
        if msg:
            # Convert msg to a regular dict to make it mutable
            msg = dict(msg)

            # Format datetime fields
            for key, value in msg.items():
                if isinstance(value, datetime):
                    msg[key] = value.strftime('%Y-%m-%dT%H:%M:%S.000Z')

            # Add file URL for client-side display
            msg['file_url'] = f"/api/files/{msg_id}"

        # Emit message to both sender and receiver
        # Always convert IDs to strings to ensure proper room targeting
        sender_room = str(sender_id)
        receiver_room = str(receiver_id)

        # Debug logging
        print(f"Emitting file message to sender room: {sender_room}")
        print(f"Emitting file message to receiver room: {receiver_room}")
        print(f"Emitting file message to sender SID: {request.sid}")
        print(f"File message content: {msg}")

        # Send to receiver's room
        emit('receive_message', msg, room=receiver_room)

        # Send to sender's room (in case they have multiple tabs/devices)
        # But not to the current session to avoid duplication
        if sender_room != request.sid:
            emit('receive_message', msg, room=sender_room)

        # Also send to sender's session for immediate feedback
        # But only if it's not already covered by the sender_room emit
        if request.sid not in [sender_room, receiver_room]:
            emit('receive_message', msg, room=request.sid)

        # Broadcast a debug event to help troubleshoot
        emit('message_debug', {
            'event': 'file_message_sent',
            'message_id': msg['id'],
            'sender_id': sender_id,
            'receiver_id': receiver_id,
            'sender_room': sender_room,
            'receiver_room': receiver_room,
            'file_name': file_name,
            'timestamp': datetime.now().strftime('%Y-%m-%dT%H:%M:%S.000Z')
        }, broadcast=True)

        # Schedule a task to update the message status to 'sent' after a short delay
        # This allows the clock icon to be displayed for a moment before changing to checkmark
        def update_file_message_status():
            try:
                time.sleep(2)  # Wait 2 seconds
                conn = get_db_connection()
                cursor = conn.cursor(dictionary=True)

                # Update the message status to 'sent'
                update_query = """
                    UPDATE messages
                    SET status = 'sent'
                    WHERE id = %s
                """
                cursor.execute(update_query, (msg_id,))
                conn.commit()

                # Get the updated message (without file_data to reduce payload size)
                select_query = """
                    SELECT
                        id, sender_id, sender_type, receiver_id, receiver_type,
                        message_text as message, is_read, is_auto,
                        related_to_job_id, related_to_application_id,
                        message_type, timestamp, status, is_deleted,
                        deleted_by_sender, deleted_by_receiver,
                        file_name, file_mime_type, file_url
                    FROM messages
                    WHERE id = %s
                """
                cursor.execute(select_query, (msg_id,))
                updated_msg = cursor.fetchone()

                if updated_msg:
                    # Format the message for JSON serialization
                    formatted_msg = dict(updated_msg)
                    for key, value in formatted_msg.items():
                        if isinstance(value, datetime):
                            formatted_msg[key] = value.strftime('%Y-%m-%dT%H:%M:%S.000Z')

                    # Emit a status update event instead of a full message
                    # This prevents duplicate messages in the UI
                    # Send to both sender and receiver rooms to ensure both see the updated status
                    socketio.emit('message_status_update', {
                        'message_id': msg_id,
                        'status': 'sent'
                    }, room=sender_room)

                    # Also send to receiver room to update their view
                    socketio.emit('message_status_update', {
                        'message_id': msg_id,
                        'status': 'sent'
                    }, room=receiver_room)

                    # And to the current session if needed
                    if request.sid not in [sender_room, receiver_room]:
                        socketio.emit('message_status_update', {
                            'message_id': msg_id,
                            'status': 'sent'
                        }, room=request.sid)

                cursor.close()
                conn.close()
            except Exception as e:
                print(f"Error updating file message status: {e}")

        # Start the status update in a separate thread
        status_thread = threading.Thread(target=update_file_message_status)
        status_thread.daemon = True
        status_thread.start()

        return msg

    except Exception as e:
        print(f"Error sending file message: {e}")
        emit('error', {'msg': f'Error sending file: {str(e)}'})
        return None

@app.route('/get_notification_history')
def get_notification_history():
    if 'user_id' not in session or session.get('user_type') != 'genius':
        return jsonify({'success': False, 'error': 'Unauthorized'}), 401

    genius_id = session['user_id']

    try:
        conn = get_db_connection()
        cursor = conn.cursor(dictionary=True)

        # Get all accepted/declined applications for this genius
        query = """
            SELECT a.id, a.job_id, a.status, a.created_at, a.updated_at,
                   js.title as job_title, ac.business_name as company_name, js.client_id,
                   ac.first_name as client_first_name, ac.last_name as client_last_name,
                   ac.business_name
            FROM applications a
            JOIN job_submissions js ON a.job_id = js.id
            LEFT JOIN approve_client ac ON js.client_id = ac.id
            WHERE a.genius_id = %s
            AND a.status IN ('accepted', 'declined', 'accept', 'reject')
            ORDER BY COALESCE(a.updated_at, a.created_at) DESC
            LIMIT 50
        """

        cursor.execute(query, (genius_id,))
        notifications = cursor.fetchall()

        return jsonify({
            'success': True,
            'notifications': notifications
        })

    except Exception as e:
        print(f"Error getting notification history: {e}")
        return jsonify({'success': False, 'error': str(e)}), 500
    finally:
        if 'cursor' in locals():
            cursor.close()
        if 'conn' in locals():
            conn.close()



@app.route('/simulate_status_change/<int:job_id>/<status>')
def simulate_status_change(job_id, status):
    """Simulate changing an application status for testing"""
    if 'user_id' not in session or session.get('user_type') != 'genius':
        return jsonify({'success': False, 'error': 'Unauthorized'}), 401

    genius_id = session['user_id']

    try:
        conn = get_db_connection()
        cursor = conn.cursor()

        # Update the application status
        update_query = """
            UPDATE applications
            SET status = %s, updated_at = NOW()
            WHERE job_id = %s AND genius_id = %s
        """

        cursor.execute(update_query, (status, job_id, genius_id))

        if cursor.rowcount > 0:
            conn.commit()
            return jsonify({
                'success': True,
                'message': f'Application status changed to {status} for job {job_id}'
            })
        else:
            return jsonify({
                'success': False,
                'error': 'No application found or no changes made'
            })

    except Exception as e:
        print(f"Error simulating status change: {e}")
        return jsonify({'success': False, 'error': str(e)}), 500
    finally:
        if 'cursor' in locals():
            cursor.close()
        if 'conn' in locals():
            conn.close()

if __name__ == '__main__':
    # Initialize file upload handler (only for the HTTP file upload route)
    handle_file_upload(app, socketio, get_db_connection)

    # Local development only
    socketio.run(app, debug=True, port=5001, allow_unsafe_werkzeug=True)


